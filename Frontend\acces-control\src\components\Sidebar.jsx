function Sidebar({ currentPage, setCurrentPage, apiStatus, onRefreshApi }) {
  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: '📊' },
    { id: 'personnel', label: 'Personnel', icon: '👥' },
    { id: 'badges', label: 'Badges', icon: '🪪' },
    { id: 'attributions', label: 'Attributions', icon: '🔗' },
    { id: 'passages', label: 'Passages', icon: '🚪' },
    { id: 'referentiels', label: 'Référentiels', icon: '🧾' },
  ];

  return (
    <aside className="w-64 bg-gray-800 text-white flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <h2 className="text-xl font-bold">Contrôle d'Accès</h2>
        <div className="mt-2 flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            apiStatus === 'connected' ? 'bg-green-400' :
            apiStatus === 'disconnected' ? 'bg-red-400' : 'bg-yellow-400'
          }`}></div>
          <span className="text-xs text-gray-300">
            {apiStatus === 'connected' ? 'En ligne' :
             apiStatus === 'disconnected' ? 'Hors ligne' : 'Connexion...'}
          </span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <div className="space-y-2">
          {menuItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setCurrentPage(item.id)}
              className={`w-full flex items-center space-x-3 py-3 px-4 rounded-lg text-left transition-colors ${
                currentPage === item.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
            >
              <span className="text-lg">{item.icon}</span>
              <span className="font-medium">{item.label}</span>
            </button>
          ))}
        </div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-700">
        <button
          onClick={onRefreshApi}
          className="w-full flex items-center justify-center space-x-2 py-2 px-4 bg-gray-700 hover:bg-gray-600 rounded-lg text-sm transition-colors"
        >
          <span>↻</span>
          <span>Actualiser API</span>
        </button>

        <div className="mt-3 text-xs text-gray-400 text-center">
          <div>Backend: localhost:3000</div>
          <div className="mt-1">v1.0.0</div>
        </div>
      </div>
    </aside>
  );
}

export default Sidebar;