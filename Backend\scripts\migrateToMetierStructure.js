const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function migrateToMetierStructure() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Début de la migration vers la structure métier...');
    
    // Lire le script SQL de la nouvelle structure
    const sqlScript = fs.readFileSync(
      path.join(__dirname, '../../Docs/init_access_control_metier.sql'), 
      'utf8'
    );
    
    // Exécuter le script complet
    console.log('📝 Exécution du script de migration...');
    await client.query(sqlScript);
    
    console.log('✅ Structure métier créée avec succès !');
    
    // Insérer des données de test selon la nouvelle logique métier
    await insertTestDataMetier(client);
    
  } catch (error) {
    console.error('❌ Erreur lors de la migration:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

async function insertTestDataMetier(client) {
  console.log('📊 Insertion des données de test métier...');
  
  try {
    // Badges selon la nouvelle logique
    console.log('🪪 Création des badges...');
    
    // Badges militaires internes (permanents, non réutilisables)
    for (let i = 1; i <= 10; i++) {
      await client.query(
        'INSERT INTO badge (epc_code, id_type_badge) VALUES ($1, $2)',
        [`E200001617501${i.toString().padStart(3, '0')}`, 1]
      );
    }
    
    // Badges visiteurs militaires (temporaires, réutilisables)
    for (let i = 1; i <= 5; i++) {
      await client.query(
        'INSERT INTO badge (epc_code, id_type_badge) VALUES ($1, $2)',
        [`E200001617502${i.toString().padStart(3, '0')}`, 2]
      );
    }
    
    // Badges visiteurs civils (temporaires, réutilisables)
    for (let i = 1; i <= 5; i++) {
      await client.query(
        'INSERT INTO badge (epc_code, id_type_badge) VALUES ($1, $2)',
        [`E200001617503${i.toString().padStart(3, '0')}`, 3]
      );
    }
    
    // Personnel selon la nouvelle typologie
    console.log('👥 Création du personnel...');
    
    // Militaires internes
    const militairesInternes = [
      ['Dupont', 'Jean', 5, 1], // Capitaine, État-Major
      ['Martin', 'Pierre', 6, 2], // Lieutenant, 1er RI
      ['Durand', 'Marie', 4, 1], // Commandant, État-Major
      ['Moreau', 'Paul', 7, 3], // Sous-Lieutenant, 2ème RC
      ['Bernard', 'Luc', 11, 4], // Sergent, 3ème RA
    ];
    
    for (const [nom, prenom, id_grade, id_unite] of militairesInternes) {
      const result = await client.query(
        'INSERT INTO personnel (nom, prenom, type_personnel, id_grade, id_unite) VALUES ($1, $2, $3, $4, $5) RETURNING id_personnel',
        [nom, prenom, 'militaire_interne', id_grade, id_unite]
      );
      
      // Attribution automatique d'un badge permanent
      const badgeResult = await client.query(
        'SELECT id_badge FROM v_badges_disponibles WHERE id_type_badge = 1 LIMIT 1'
      );
      
      if (badgeResult.rows.length > 0) {
        await client.query(
          'INSERT INTO attribution_badge (id_personnel, id_badge, attribue_par) VALUES ($1, $2, $3)',
          [result.rows[0].id_personnel, badgeResult.rows[0].id_badge, 'Service des badges']
        );
      }
    }
    
    // Militaires externes (visiteurs)
    const militairesExternes = [
      {
        personnel: ['Colonel', 'Robert', 2, 1],
        info: ['12345678', '5ème Régiment Blindé', 'État-Major', 'Réunion stratégique', '2025-07-08 09:00:00', '2025-07-08 17:00:00', 'Commandant Durand']
      },
      {
        personnel: ['Commandant', 'Michel', 4, 2],
        info: ['87654321', '7ème Régiment Parachutiste', 'Service Technique', 'Formation technique', '2025-07-08 14:00:00', '2025-07-08 18:00:00', 'Capitaine Martin']
      }
    ];
    
    for (const militaire of militairesExternes) {
      const [nom, prenom, id_grade, id_unite] = militaire.personnel;
      const [cin, unite_origine, destination, objet_visite, horaire_entree, horaire_sortie_prevue, accompagnateur] = militaire.info;
      
      const result = await client.query(
        'INSERT INTO personnel (nom, prenom, type_personnel, id_grade, id_unite) VALUES ($1, $2, $3, $4, $5) RETURNING id_personnel',
        [nom, prenom, 'militaire_externe', id_grade, id_unite]
      );
      
      await client.query(
        'INSERT INTO militaire_externe_info (id_personnel, cin, unite_origine, destination, objet_visite, horaire_entree, horaire_sortie_prevue, accompagnateur) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)',
        [result.rows[0].id_personnel, cin, unite_origine, destination, objet_visite, horaire_entree, horaire_sortie_prevue, accompagnateur]
      );
      
      // Attribution manuelle d'un badge visiteur militaire
      const badgeResult = await client.query(
        'SELECT id_badge FROM v_badges_disponibles WHERE id_type_badge = 2 LIMIT 1'
      );
      
      if (badgeResult.rows.length > 0) {
        await client.query(
          'INSERT INTO attribution_badge (id_personnel, id_badge, attribue_par) VALUES ($1, $2, $3)',
          [result.rows[0].id_personnel, badgeResult.rows[0].id_badge, 'Agent point d\'accès']
        );
      }
    }
    
    // Civils externes
    const civilsExternes = [
      {
        personnel: ['Technicien', 'David', null, null],
        info: ['11223344', 'TechnoServ SARL', 'Service Technique', 'Maintenance équipements', '2025-07-08 08:00:00', '2025-07-08 16:00:00', 'Sergent Bernard']
      },
      {
        personnel: ['Consultant', 'Emma', null, null],
        info: ['44332211', 'ConseilPro SA', 'État-Major', 'Audit sécurité', '2025-07-08 10:00:00', '2025-07-08 15:00:00', 'Commandant Durand']
      }
    ];
    
    for (const civil of civilsExternes) {
      const [nom, prenom] = civil.personnel;
      const [cin, societe, destination, objet_visite, horaire_entree, horaire_sortie_prevue, accompagnateur] = civil.info;
      
      const result = await client.query(
        'INSERT INTO personnel (nom, prenom, type_personnel) VALUES ($1, $2, $3) RETURNING id_personnel',
        [nom, prenom, 'civil_externe']
      );
      
      await client.query(
        'INSERT INTO civil_info (id_personnel, cin, societe, destination, objet_visite, horaire_entree, horaire_sortie_prevue, accompagnateur) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)',
        [result.rows[0].id_personnel, cin, societe, destination, objet_visite, horaire_entree, horaire_sortie_prevue, accompagnateur]
      );
      
      // Attribution manuelle d'un badge visiteur civil
      const badgeResult = await client.query(
        'SELECT id_badge FROM v_badges_disponibles WHERE id_type_badge = 3 LIMIT 1'
      );
      
      if (badgeResult.rows.length > 0) {
        await client.query(
          'INSERT INTO attribution_badge (id_personnel, id_badge, attribue_par) VALUES ($1, $2, $3)',
          [result.rows[0].id_personnel, badgeResult.rows[0].id_badge, 'Agent d\'accueil']
        );
      }
    }
    
    // Passages de test
    console.log('🚪 Création des passages...');
    
    const passages = [
      // Passages autorisés
      [1, 1, '2025-07-08 08:00:00', 'entree', 'autorise'],
      [2, 1, '2025-07-08 08:15:00', 'entree', 'autorise'],
      [3, 1, '2025-07-08 08:30:00', 'entree', 'autorise'],
      [11, 1, '2025-07-08 09:00:00', 'entree', 'autorise'], // Militaire externe
      [13, 1, '2025-07-08 10:00:00', 'entree', 'autorise'], // Civil externe
      [1, 2, '2025-07-08 12:00:00', 'sortie', 'autorise'],
      [2, 2, '2025-07-08 12:15:00', 'sortie', 'autorise'],
      
      // Passages refusés (badges inconnus)
      [null, 1, '2025-07-08 08:45:00', 'entree', 'refuse'],
      [null, 1, '2025-07-08 14:30:00', 'entree', 'refuse']
    ];
    
    for (const [id_badge, id_porte, date_acces, type_acces, resultat] of passages) {
      let epc_code = null;
      let motif_refus = null;
      
      if (id_badge) {
        const badgeResult = await client.query('SELECT epc_code FROM badge WHERE id_badge = $1', [id_badge]);
        if (badgeResult.rows.length > 0) {
          epc_code = badgeResult.rows[0].epc_code;
        }
      } else {
        epc_code = 'E200001617509999'; // Badge inconnu
        motif_refus = 'Badge non reconnu';
      }
      
      await client.query(
        'INSERT INTO passage (id_badge, id_porte, epc_code, date_acces, type_acces, resultat, motif_refus) VALUES ($1, $2, $3, $4, $5, $6, $7)',
        [id_badge, id_porte, epc_code, date_acces, type_acces, resultat, motif_refus]
      );
    }
    
    // Afficher les statistiques
    console.log('\n📈 STATISTIQUES DE LA MIGRATION:');
    
    const stats = await client.query(`
      SELECT 'Personnel Total' as type, COUNT(*) as count FROM personnel
      UNION ALL SELECT 'Militaires Internes', COUNT(*) FROM personnel WHERE type_personnel = 'militaire_interne'
      UNION ALL SELECT 'Militaires Externes', COUNT(*) FROM personnel WHERE type_personnel = 'militaire_externe'
      UNION ALL SELECT 'Civils Externes', COUNT(*) FROM personnel WHERE type_personnel = 'civil_externe'
      UNION ALL SELECT 'Badges Total', COUNT(*) FROM badge
      UNION ALL SELECT 'Badges Permanents', COUNT(*) FROM badge b JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge WHERE tb.permanent = TRUE
      UNION ALL SELECT 'Badges Réutilisables', COUNT(*) FROM badge b JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge WHERE tb.reutilisable = TRUE
      UNION ALL SELECT 'Attributions Actives', COUNT(*) FROM attribution_badge WHERE statut = 'actif'
      UNION ALL SELECT 'Passages Total', COUNT(*) FROM passage
    `);
    
    stats.rows.forEach(row => {
      console.log(`   ${row.type}: ${row.count}`);
    });
    
    // Afficher quelques exemples d'attributions
    const attributions = await client.query(`
      SELECT nom, prenom, type_personnel, epc_code, nom_type_badge, permanent, attribue_par
      FROM v_attributions_actives 
      ORDER BY type_personnel, nom
      LIMIT 8
    `);
    
    console.log('\n👥 EXEMPLES D\'ATTRIBUTIONS ACTIVES:');
    attributions.rows.forEach(row => {
      const badgeType = row.permanent ? '🔒 Permanent' : '🔄 Temporaire';
      console.log(`   ${row.nom} ${row.prenom} (${row.type_personnel}) - Badge: ${row.epc_code} (${row.nom_type_badge}) ${badgeType} - Par: ${row.attribue_par}`);
    });
    
    console.log('\n✅ Migration métier terminée avec succès !');
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'insertion des données de test:', error);
    throw error;
  }
}

// Exécuter la migration
migrateToMetierStructure();
