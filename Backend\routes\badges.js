const express = require('express');
const router = express.Router();
const { 
  createBadge,
  getAllBadges,
  getBadgesDisponibles,
  getBadgeById,
  updateBadge,
  deleteBadge
} = require('../controllers/badgeController');

// ========== ROUTES GESTION DES BADGES ==========

// POST /api/badges - Créer un badge avec type (interne/externe/militaire/civil)
router.post('/', createBadge);

// GET /api/badges - Lister tous les badges
router.get('/', getAllBadges);

// GET /api/badges/disponibles - Lister les badges non attribués (libres)
router.get('/disponibles', getBadgesDisponibles);

// GET /api/badges/:id - Détails d'un badge
router.get('/:id', getBadgeById);

// PUT /api/badges/:id - Modifier un badge
router.put('/:id', updateBadge);

// DELETE /api/badges/:id - Supprimer un badge (s'il n'est pas actif)
router.delete('/:id', deleteBadge);

module.exports = router;
