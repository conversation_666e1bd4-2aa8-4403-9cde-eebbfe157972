const db = require('../models/db');

// ========== PASSAGES / CONTRÔLE D'ACCÈS ==========

// Enregistrer un passage (C72 envoie EPC, porte, date, type accès)
const createPassage = async (req, res) => {
  const { epc_code, id_porte, type_acces, date_acces } = req.body;
  
  try {
    // 1. Trouver le badge par son EPC
    const badgeResult = await db.query('SELECT * FROM badge WHERE epc_code = $1', [epc_code]);
    
    if (badgeResult.rows.length === 0) {
      // Badge non trouvé -> accès refusé
      const passageResult = await db.query(
        'INSERT INTO passage (id_badge, id_porte, date_acces, type_acces, resultat) VALUES (NULL, $1, $2, $3, $4) RETURNING *',
        [id_porte, date_acces || new Date(), type_acces, 'refusé']
      );
      
      return res.status(200).json({
        passage: passageResult.rows[0],
        resultat: 'refusé',
        motif: 'Badge non reconnu',
        epc_code
      });
    }
    
    const badge = badgeResult.rows[0];
    
    // 2. Vérifier si le badge est actuellement attribué et actif
    const attributionResult = await db.query(`
      SELECT ab.*, p.nom, p.prenom 
      FROM attribution_badge ab
      JOIN personnel p ON ab.id_personnel = p.id_personnel
      WHERE ab.id_badge = $1 AND ab.statut = 'actif'
    `, [badge.id_badge]);
    
    let resultat = 'refusé';
    let motif = 'Badge non attribué ou inactif';
    
    if (attributionResult.rows.length > 0) {
      // Badge actif -> accès autorisé
      resultat = 'autorisé';
      motif = 'Accès autorisé';
    }
    
    // 3. Enregistrer le passage
    const passageResult = await db.query(
      'INSERT INTO passage (id_badge, id_porte, date_acces, type_acces, resultat) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [badge.id_badge, id_porte, date_acces || new Date(), type_acces, resultat]
    );
    
    // 4. Retourner le résultat avec les informations complètes
    const response = {
      passage: passageResult.rows[0],
      resultat,
      motif,
      badge: {
        id_badge: badge.id_badge,
        epc_code: badge.epc_code
      }
    };
    
    if (attributionResult.rows.length > 0) {
      response.personnel = {
        nom: attributionResult.rows[0].nom,
        prenom: attributionResult.rows[0].prenom
      };
    }
    
    res.status(201).json(response);
    
  } catch (error) {
    console.error('❌ Erreur enregistrement passage :', error);
    res.status(500).json({ error: 'Erreur lors de l\'enregistrement du passage' });
  }
};

// Lister tous les passages
const getAllPassages = async (req, res) => {
  const { page = 1, limit = 100, resultat, date_debut, date_fin } = req.query;
  const offset = (page - 1) * limit;
  
  try {
    let query = `
      SELECT pa.*, 
             b.epc_code,
             po.libelle as nom_porte,
             p.nom, p.prenom, tp.nom_type as type_personnel
      FROM passage pa
      LEFT JOIN badge b ON pa.id_badge = b.id_badge
      LEFT JOIN porte po ON pa.id_porte = po.id_porte
      LEFT JOIN attribution_badge ab ON b.id_badge = ab.id_badge AND ab.statut = 'actif'
      LEFT JOIN personnel p ON ab.id_personnel = p.id_personnel
      LEFT JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
      WHERE 1=1
    `;
    
    const params = [];
    let paramIndex = 1;
    
    if (resultat) {
      query += ` AND pa.resultat = $${paramIndex}`;
      params.push(resultat);
      paramIndex++;
    }
    
    if (date_debut) {
      query += ` AND pa.date_acces >= $${paramIndex}`;
      params.push(date_debut);
      paramIndex++;
    }
    
    if (date_fin) {
      query += ` AND pa.date_acces <= $${paramIndex}`;
      params.push(date_fin);
      paramIndex++;
    }
    
    query += ` ORDER BY pa.date_acces DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, offset);
    
    const result = await db.query(query, params);
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération passages :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Lister les passages récents (temps réel : ex. les 20 derniers)
const getPassagesRecents = async (req, res) => {
  const { limit = 20 } = req.query;
  
  try {
    const result = await db.query(`
      SELECT pa.*, 
             b.epc_code,
             po.libelle as nom_porte,
             p.nom, p.prenom, tp.nom_type as type_personnel
      FROM passage pa
      LEFT JOIN badge b ON pa.id_badge = b.id_badge
      LEFT JOIN porte po ON pa.id_porte = po.id_porte
      LEFT JOIN attribution_badge ab ON b.id_badge = ab.id_badge AND ab.statut = 'actif'
      LEFT JOIN personnel p ON ab.id_personnel = p.id_personnel
      LEFT JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
      ORDER BY pa.date_acces DESC
      LIMIT $1
    `, [limit]);
    
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération passages récents :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Historique des passages d'un personnel
const getPassagesPersonnel = async (req, res) => {
  const { id_personnel } = req.params;
  const { page = 1, limit = 50 } = req.query;
  const offset = (page - 1) * limit;
  
  try {
    const result = await db.query(`
      SELECT pa.*, 
             b.epc_code,
             po.libelle as nom_porte
      FROM passage pa
      JOIN badge b ON pa.id_badge = b.id_badge
      JOIN attribution_badge ab ON b.id_badge = ab.id_badge
      LEFT JOIN porte po ON pa.id_porte = po.id_porte
      WHERE ab.id_personnel = $1
      ORDER BY pa.date_acces DESC
      LIMIT $2 OFFSET $3
    `, [id_personnel, limit, offset]);
    
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération passages personnel :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Historique d'un badge
const getPassagesBadge = async (req, res) => {
  const { id_badge } = req.params;
  const { page = 1, limit = 50 } = req.query;
  const offset = (page - 1) * limit;
  
  try {
    const result = await db.query(`
      SELECT pa.*, 
             po.libelle as nom_porte,
             p.nom, p.prenom
      FROM passage pa
      LEFT JOIN porte po ON pa.id_porte = po.id_porte
      LEFT JOIN attribution_badge ab ON pa.id_badge = ab.id_badge
      LEFT JOIN personnel p ON ab.id_personnel = p.id_personnel
      WHERE pa.id_badge = $1
      ORDER BY pa.date_acces DESC
      LIMIT $2 OFFSET $3
    `, [id_badge, limit, offset]);
    
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération passages badge :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Statistiques des passages (optionnel)
const getStatistiquesPassages = async (req, res) => {
  const { date_debut, date_fin } = req.query;
  
  try {
    let whereClause = '';
    const params = [];
    
    if (date_debut && date_fin) {
      whereClause = 'WHERE date_acces BETWEEN $1 AND $2';
      params.push(date_debut, date_fin);
    }
    
    const result = await db.query(`
      SELECT 
        COUNT(*) as total_passages,
        COUNT(CASE WHEN resultat = 'autorisé' THEN 1 END) as passages_autorises,
        COUNT(CASE WHEN resultat = 'refusé' THEN 1 END) as passages_refuses,
        COUNT(DISTINCT id_badge) as badges_utilises,
        COUNT(DISTINCT id_porte) as portes_utilisees
      FROM passage
      ${whereClause}
    `, params);
    
    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur statistiques passages :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

module.exports = {
  createPassage,
  getAllPassages,
  getPassagesRecents,
  getPassagesPersonnel,
  getPassagesBadge,
  getStatistiquesPassages,
};
