# Documentation des APIs - Système de Contrôle d'Accès

## 🚀 Serveur Backend
- **URL de base** : `http://localhost:3000`
- **Port** : 3000
- **Base de données** : PostgreSQL
- **Status** : ✅ Opérationnel

## 📋 APIs Implémentées

### 🔧 API de Test
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/ping` | Test de connectivité de l'API |

---

## 👤 1. API - Gestion des Personnels

### Personnel Interne
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| POST | `/api/personnel/internes` | Créer un personnel interne avec badge automatiquement attribué |
| GET | `/api/personnel/internes` | Liste des personnels internes |
| GET | `/api/personnel/internes/:id` | Détails d'un personnel interne |
| PUT | `/api/personnel/internes/:id` | Modifier un personnel interne |
| DELETE | `/api/personnel/internes/:id` | Supprimer un personnel interne (logique ou physique) |

### Personnel Externe (militaire ou civil)
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| POST | `/api/personnel/externes` | Créer un personnel externe (badge sélectionné manuellement) |
| GET | `/api/personnel/externes` | Liste des personnels externes |
| GET | `/api/personnel/externes/:id` | Détails d'un personnel externe |
| PUT | `/api/personnel/externes/:id` | Modifier un personnel externe |
| DELETE | `/api/personnel/externes/:id` | Supprimer un personnel externe |

### Routes de Compatibilité
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/personnels` | Liste de tous les personnels (ancienne route) |
| POST | `/api/personnels` | Créer un personnel (fonction générique) |

---

## 🪪 2. API - Gestion des Badges
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| POST | `/api/badges` | Créer un badge avec type (interne/externe/militaire/civil) |
| GET | `/api/badges` | Lister tous les badges |
| GET | `/api/badges/disponibles` | Lister les badges non attribués (libres) |
| GET | `/api/badges/:id` | Détails d'un badge |
| PUT | `/api/badges/:id` | Modifier un badge |
| DELETE | `/api/badges/:id` | Supprimer un badge (s'il n'est pas actif) |

---

## 🔄 3. API - Attribution des Badges
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| POST | `/api/attributions` | Attribuer un badge à un personnel (manuel ou automatique) |
| PUT | `/api/attributions/:id/fin` | Clôturer une attribution (date de fin + statut inactif) |
| GET | `/api/attributions` | Lister toutes les attributions |
| GET | `/api/attributions/:id` | Détails d'une attribution |
| GET | `/api/attributions/actives` | Lister les attributions actives |
| GET | `/api/attributions/personnel/:id_personnel` | Historique des badges d'un personnel |

---

## 🚪 4. API - Passages / Contrôle d'Accès
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| POST | `/api/passages` | Enregistrer un passage (C72 envoie EPC, porte, date, type accès) |
| GET | `/api/passages` | Lister tous les passages |
| GET | `/api/passages/recent` | Lister les passages récents (temps réel : ex. les 20 derniers) |
| GET | `/api/passages/personnel/:id_personnel` | Historique des passages d'un personnel |
| GET | `/api/passages/badge/:id_badge` | Historique d'un badge |
| GET | `/api/passages/statistiques` | Statistiques des passages |

---

## 🧾 5. API - Référentiels

### Types de Badge
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/types_badge` | Lister les types de badge |
| POST | `/api/types_badge` | Créer un type de badge |

### Types de Personnel
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/types_personnel` | Lister les types de personnel |
| POST | `/api/types_personnel` | Créer un type de personnel |

### Unités
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/unites` | Lister les unités |
| POST | `/api/unites` | Créer une unité |
| PUT | `/api/unites/:id` | Modifier une unité |
| DELETE | `/api/unites/:id` | Supprimer une unité |

### Grades
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/grades` | Lister les grades |
| POST | `/api/grades` | Créer un grade |
| PUT | `/api/grades/:id` | Modifier un grade |
| DELETE | `/api/grades/:id` | Supprimer un grade |

### Portes
| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/portes` | Lister les portes disponibles |
| POST | `/api/portes` | Créer une porte |
| PUT | `/api/portes/:id` | Modifier une porte |
| DELETE | `/api/portes/:id` | Supprimer une porte |

---

## 📊 Structure de la Base de Données

### Tables Principales
- **Personnel** : Informations des personnels (interne/externe)
- **Badge** : Badges RFID avec codes EPC
- **Attribution_Badge** : Liaison personnel-badge avec historique
- **Passage** : Journal des passages aux portes
- **Type_Personnel** : Types (interne, externe, militaire, civil)
- **Type_Badge** : Types de badges
- **Grade** : Grades militaires/civils
- **Unite** : Unités d'affectation
- **Porte** : Points de contrôle d'accès

---

## 🔧 Fonctionnalités Spéciales

### Attribution Automatique de Badges
- **Personnel Interne** : Badge attribué automatiquement lors de la création
- **Personnel Externe** : Badge sélectionné manuellement

### Contrôle d'Accès Intelligent
- Vérification automatique de la validité des badges
- Journalisation de tous les passages (autorisés/refusés)
- Gestion des badges perdus ou désactivés

### Gestion des Suppressions
- **Suppression logique** : Désactivation des attributions (par défaut)
- **Suppression physique** : Suppression définitive (sur demande)

---

## ✅ Status des Tests
- ✅ Serveur opérationnel
- ✅ Connexion PostgreSQL établie
- ✅ Toutes les routes fonctionnelles
- ✅ APIs testées et validées

## 🚀 Démarrage
```bash
cd Backend
node app.js
```

Le serveur démarre sur `http://localhost:3000` avec confirmation de connexion à PostgreSQL.
