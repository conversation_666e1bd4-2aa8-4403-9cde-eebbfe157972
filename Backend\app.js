const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
require('dotenv').config();
require('./models/db');

const app = express();
const port = process.env.PORT || 3000;

// Middlewares
app.use(cors());
app.use(bodyParser.json());

// Routes
const pingRoute = require('./routes/ping');
app.use('/api/ping', pingRoute);

// ========== ROUTES PRINCIPALES ==========

// Personnel (interne et externe)
const personnelsRoute = require('./routes/personnels');
app.use('/api/personnel', personnelsRoute); // Nouvelle route selon spécifications
app.use('/api/personnels', personnelsRoute); // Ancienne route maintenue pour compatibilité

// Badges
const badgesRoute = require('./routes/badges');
app.use('/api/badges', badgesRoute);

// Attributions de badges
const attributionsRoute = require('./routes/attributions');
app.use('/api/attributions', attributionsRoute);

// Passages / Contrôle d'accès
const passagesRoute = require('./routes/passages');
app.use('/api/passages', passagesRoute);

// Référentiels (types, grades, unités, portes)
const referentielsRoute = require('./routes/referentiels');
app.use('/api', referentielsRoute);

// Lancement du serveur
app.listen(port, () => {
  console.log(`✅ Backend opérationnel sur http://localhost:${port}`);
});
