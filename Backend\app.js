const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
require('dotenv').config();
require('./models/db');

const app = express();
const port = process.env.PORT || 3000;

// Middlewares
app.use(cors());
app.use(bodyParser.json());

// Routes
const pingRoute = require('./routes/ping');
app.use('/api/ping', pingRoute);

const personnelsRoute = require('./routes/personnels');
app.use('/api/personnels', personnelsRoute);

// Lancement du serveur
app.listen(port, () => {
  console.log(`✅ Backend opérationnel sur http://localhost:${port}`);
});
