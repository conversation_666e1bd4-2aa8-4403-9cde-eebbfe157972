import { useState, useEffect } from 'react';
import { badgeAPI, referentielAPI } from '../services/api';

function BadgeManagement() {
  const [badges, setBadges] = useState([]);
  const [badgesDisponibles, setBadgesDisponibles] = useState([]);
  const [typesBadge, setTypesBadge] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [filter, setFilter] = useState('all'); // all, disponibles, attribues
  const [newBadge, setNewBadge] = useState({
    epc_code: '',
    id_type_badge: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [badgesRes, badgesDispRes, typesRes] = await Promise.all([
        badgeAPI.getAllBadges(),
        badgeAPI.getBadgesDisponibles(),
        referentielAPI.getTypesBadge()
      ]);
      
      setBadges(badgesRes.data);
      setBadgesDisponibles(badgesDispRes.data);
      setTypesBadge(typesRes.data);
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBadge = async (e) => {
    e.preventDefault();
    try {
      await badgeAPI.createBadge(newBadge);
      setNewBadge({ epc_code: '', id_type_badge: '' });
      setShowCreateForm(false);
      loadData();
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      alert('Erreur lors de la création du badge');
    }
  };

  const handleDeleteBadge = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce badge ?')) {
      try {
        await badgeAPI.deleteBadge(id);
        loadData();
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        alert('Erreur lors de la suppression du badge');
      }
    }
  };

  const filteredBadges = badges.filter(badge => {
    if (filter === 'disponibles') return badge.statut_attribution === 'libre';
    if (filter === 'attribues') return badge.statut_attribution === 'attribué';
    return true;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Chargement...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Gestion des Badges</h2>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          + Nouveau Badge
        </button>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-blue-600">{badges.length}</div>
          <div className="text-gray-600">Total Badges</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">{badgesDisponibles.length}</div>
          <div className="text-gray-600">Badges Disponibles</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-orange-600">
            {badges.filter(b => b.statut_attribution === 'attribué').length}
          </div>
          <div className="text-gray-600">Badges Attribués</div>
        </div>
      </div>

      {/* Filtres */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex space-x-4">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded ${filter === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
          >
            Tous ({badges.length})
          </button>
          <button
            onClick={() => setFilter('disponibles')}
            className={`px-4 py-2 rounded ${filter === 'disponibles' ? 'bg-green-600 text-white' : 'bg-gray-200'}`}
          >
            Disponibles ({badgesDisponibles.length})
          </button>
          <button
            onClick={() => setFilter('attribues')}
            className={`px-4 py-2 rounded ${filter === 'attribues' ? 'bg-orange-600 text-white' : 'bg-gray-200'}`}
          >
            Attribués ({badges.filter(b => b.statut_attribution === 'attribué').length})
          </button>
        </div>
      </div>

      {/* Liste des badges */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Code EPC</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Statut</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Attribué à</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {filteredBadges.map((badge) => (
              <tr key={badge.id_badge}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {badge.id_badge}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                  {badge.epc_code}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {badge.nom_type_badge}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    badge.statut_attribution === 'libre' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-orange-100 text-orange-800'
                  }`}>
                    {badge.statut_attribution === 'libre' ? 'Disponible' : 'Attribué'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {badge.nom && badge.prenom ? `${badge.nom} ${badge.prenom}` : '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => handleDeleteBadge(badge.id_badge)}
                    disabled={badge.statut_attribution === 'attribué'}
                    className={`text-red-600 hover:text-red-900 ${
                      badge.statut_attribution === 'attribué' ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    Supprimer
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Modal de création */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg shadow-lg w-96">
            <h3 className="text-lg font-medium mb-4">Créer un nouveau badge</h3>
            <form onSubmit={handleCreateBadge}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Code EPC
                </label>
                <input
                  type="text"
                  value={newBadge.epc_code}
                  onChange={(e) => setNewBadge({...newBadge, epc_code: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="E200001617501001"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Type de badge
                </label>
                <select
                  value={newBadge.id_type_badge}
                  onChange={(e) => setNewBadge({...newBadge, id_type_badge: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                >
                  <option value="">Sélectionner un type</option>
                  {typesBadge.map((type) => (
                    <option key={type.id_type_badge} value={type.id_type_badge}>
                      {type.nom_type_badge}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Créer
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default BadgeManagement;
