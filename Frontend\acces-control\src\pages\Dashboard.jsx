import { useState, useEffect } from 'react';
import { personnelAPI, badgeAPI, passageAPI, attributionAPI } from '../services/api';

function Dashboard() {
  const [stats, setStats] = useState({
    totalPersonnels: 0,
    personnelsInternes: 0,
    personnelsExternes: 0,
    totalBadges: 0,
    badgesDisponibles: 0,
    attributionsActives: 0,
    passagesAujourdhui: 0,
    passagesAutorises: 0,
    passagesRefuses: 0
  });
  const [passagesRecents, setPassagesRecents] = useState([]);
  const [attributionsRecentes, setAttributionsRecentes] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
    // Actualiser les données toutes les 30 secondes
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Charger toutes les données en parallèle
      const [
        personnelsRes,
        internesRes,
        externesRes,
        badgesRes,
        badgesDispRes,
        attributionsRes,
        passagesRes,
        passagesRecentsRes
      ] = await Promise.all([
        personnelAPI.getAllPersonnels(),
        personnelAPI.getPersonnelsInternes(),
        personnelAPI.getPersonnelsExternes(),
        badgeAPI.getAllBadges(),
        badgeAPI.getBadgesDisponibles(),
        attributionAPI.getAttributionsActives(),
        passageAPI.getAllPassages({ limit: 100 }),
        passageAPI.getPassagesRecents(10)
      ]);

      // Calculer les statistiques
      const passages = passagesRes.data;
      const today = new Date().toISOString().split('T')[0];
      const passagesAujourdhui = passages.filter(p =>
        p.date_acces.startsWith(today)
      );

      setStats({
        totalPersonnels: personnelsRes.data.length,
        personnelsInternes: internesRes.data.length,
        personnelsExternes: externesRes.data.length,
        totalBadges: badgesRes.data.length,
        badgesDisponibles: badgesDispRes.data.length,
        attributionsActives: attributionsRes.data.length,
        passagesAujourdhui: passagesAujourdhui.length,
        passagesAutorises: passages.filter(p => p.resultat === 'autorisé').length,
        passagesRefuses: passages.filter(p => p.resultat === 'refusé').length
      });

      setPassagesRecents(passagesRecentsRes.data);
      setAttributionsRecentes(attributionsRes.data.slice(0, 5));

    } catch (error) {
      console.error('Erreur lors du chargement du dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Chargement du dashboard...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header avec actualisation */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Dashboard</h2>
        <button
          onClick={loadDashboardData}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <span>↻</span>
          <span>Actualiser</span>
        </button>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="text-3xl">👥</div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-blue-600">{stats.totalPersonnels}</div>
              <div className="text-gray-600">Total Personnel</div>
              <div className="text-sm text-gray-500">
                {stats.personnelsInternes} internes, {stats.personnelsExternes} externes
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="text-3xl">🪪</div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-green-600">{stats.badgesDisponibles}</div>
              <div className="text-gray-600">Badges Disponibles</div>
              <div className="text-sm text-gray-500">
                sur {stats.totalBadges} total
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="text-3xl">🔗</div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-purple-600">{stats.attributionsActives}</div>
              <div className="text-gray-600">Attributions Actives</div>
              <div className="text-sm text-gray-500">
                badges en cours d'utilisation
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="text-3xl">🚪</div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-orange-600">{stats.passagesAujourdhui}</div>
              <div className="text-gray-600">Passages Aujourd'hui</div>
              <div className="text-sm text-gray-500">
                {stats.passagesAutorises} autorisés, {stats.passagesRefuses} refusés
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Graphiques et activité récente */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Passages récents */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium">🔴 Passages Récents (Temps Réel)</h3>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              {passagesRecents.length > 0 ? (
                passagesRecents.map((passage) => (
                  <div key={passage.id_journal} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div>
                      <div className="font-medium">
                        {passage.nom && passage.prenom ? `${passage.nom} ${passage.prenom}` : 'Personnel inconnu'}
                      </div>
                      <div className="text-sm text-gray-500">
                        {passage.nom_porte} • {new Date(passage.date_acces).toLocaleTimeString('fr-FR')}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm ${passage.type_acces === 'entree' ? 'text-blue-600' : 'text-orange-600'}`}>
                        {passage.type_acces === 'entree' ? '→' : '←'}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        passage.resultat === 'autorisé'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {passage.resultat === 'autorisé' ? '✅' : '❌'}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-500 py-8">
                  Aucun passage récent
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Attributions récentes */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium">🔗 Attributions Actives</h3>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              {attributionsRecentes.length > 0 ? (
                attributionsRecentes.map((attribution) => (
                  <div key={attribution.id_attribution} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div>
                      <div className="font-medium">
                        {attribution.nom} {attribution.prenom}
                      </div>
                      <div className="text-sm text-gray-500">
                        {attribution.type_personnel} • Badge: {attribution.epc_code}
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(attribution.date_attribution).toLocaleDateString('fr-FR')}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-500 py-8">
                  Aucune attribution active
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium mb-4">⚡ Actions Rapides</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
            <div className="text-2xl mb-2">👤</div>
            <div className="font-medium">Nouveau Personnel</div>
            <div className="text-sm text-gray-500">Ajouter un personnel</div>
          </div>
          <div className="text-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
            <div className="text-2xl mb-2">🪪</div>
            <div className="font-medium">Nouveau Badge</div>
            <div className="text-sm text-gray-500">Créer un badge</div>
          </div>
          <div className="text-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
            <div className="text-2xl mb-2">🔗</div>
            <div className="font-medium">Attribution</div>
            <div className="text-sm text-gray-500">Attribuer un badge</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Dashboard;