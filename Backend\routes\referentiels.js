const express = require('express');
const router = express.Router();
const { 
  getTypesBadge,
  createTypeBadge,
  getTypesPersonnel,
  createTypePersonnel,
  getUnites,
  createUnite,
  updateUnite,
  deleteUnite,
  getGrades,
  createGrade,
  updateGrade,
  deleteGrade,
  getPortes,
  createPorte,
  updatePorte,
  deletePorte
} = require('../controllers/referentielController');

// ========== ROUTES RÉFÉRENTIELS ==========

// ========== TYPES DE BADGE ==========
// GET /api/types_badge - Lister les types de badge
router.get('/types_badge', getTypesBadge);

// POST /api/types_badge - Créer un type de badge
router.post('/types_badge', createTypeBadge);

// ========== TYPES DE PERSONNEL ==========
// GET /api/types_personnel - Lister les types de personnel
router.get('/types_personnel', getTypesPersonnel);

// POST /api/types_personnel - Créer un type de personnel
router.post('/types_personnel', createTypePersonnel);

// ========== UNITÉS ==========
// GET /api/unites - Lister les unités
router.get('/unites', getUnites);

// POST /api/unites - Créer une unité
router.post('/unites', createUnite);

// PUT /api/unites/:id - Modifier une unité
router.put('/unites/:id', updateUnite);

// DELETE /api/unites/:id - Supprimer une unité
router.delete('/unites/:id', deleteUnite);

// ========== GRADES ==========
// GET /api/grades - Lister les grades
router.get('/grades', getGrades);

// POST /api/grades - Créer un grade
router.post('/grades', createGrade);

// PUT /api/grades/:id - Modifier un grade
router.put('/grades/:id', updateGrade);

// DELETE /api/grades/:id - Supprimer un grade
router.delete('/grades/:id', deleteGrade);

// ========== PORTES ==========
// GET /api/portes - Lister les portes disponibles
router.get('/portes', getPortes);

// POST /api/portes - Créer une porte
router.post('/portes', createPorte);

// PUT /api/portes/:id - Modifier une porte
router.put('/portes/:id', updatePorte);

// DELETE /api/portes/:id - Supprimer une porte
router.delete('/portes/:id', deletePorte);

module.exports = router;
