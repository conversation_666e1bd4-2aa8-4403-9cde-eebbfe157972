import { useState, useEffect } from 'react';
import { attributionAPI, personnelAPI, badgeAPI } from '../services/api';

function AttributionManagement() {
  const [attributions, setAttributions] = useState([]);
  const [attributionsActives, setAttributionsActives] = useState([]);
  const [personnels, setPersonnels] = useState([]);
  const [badgesDisponibles, setBadgesDisponibles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [filter, setFilter] = useState('actives'); // actives, toutes, historique
  const [newAttribution, setNewAttribution] = useState({
    id_personnel: '',
    id_badge: '',
    mode: 'manuel'
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [attributionsRes, activesRes, personnelsRes, badgesRes] = await Promise.all([
        attributionAPI.getAllAttributions(),
        attributionAPI.getAttributionsActives(),
        personnelAPI.getAllPersonnels(),
        badgeAPI.getBadgesDisponibles()
      ]);
      
      setAttributions(attributionsRes.data);
      setAttributionsActives(activesRes.data);
      setPersonnels(personnelsRes.data);
      setBadgesDisponibles(badgesRes.data);
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAttribution = async (e) => {
    e.preventDefault();
    try {
      const data = { ...newAttribution };
      if (data.mode === 'automatique') {
        delete data.id_badge; // Pas besoin de badge pour l'attribution automatique
      }
      
      await attributionAPI.createAttribution(data);
      setNewAttribution({ id_personnel: '', id_badge: '', mode: 'manuel' });
      setShowCreateForm(false);
      loadData();
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      alert('Erreur lors de la création de l\'attribution');
    }
  };

  const handleCloturerAttribution = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir clôturer cette attribution ?')) {
      try {
        await attributionAPI.cloturerAttribution(id, { motif: 'Clôture manuelle' });
        loadData();
      } catch (error) {
        console.error('Erreur lors de la clôture:', error);
        alert('Erreur lors de la clôture de l\'attribution');
      }
    }
  };

  const filteredAttributions = () => {
    if (filter === 'actives') return attributionsActives;
    if (filter === 'historique') return attributions.filter(a => a.statut !== 'actif');
    return attributions;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Chargement...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Gestion des Attributions</h2>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          + Nouvelle Attribution
        </button>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">{attributionsActives.length}</div>
          <div className="text-gray-600">Attributions Actives</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-blue-600">{attributions.length}</div>
          <div className="text-gray-600">Total Attributions</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-orange-600">{badgesDisponibles.length}</div>
          <div className="text-gray-600">Badges Disponibles</div>
        </div>
      </div>

      {/* Filtres */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex space-x-4">
          <button
            onClick={() => setFilter('actives')}
            className={`px-4 py-2 rounded ${filter === 'actives' ? 'bg-green-600 text-white' : 'bg-gray-200'}`}
          >
            Actives ({attributionsActives.length})
          </button>
          <button
            onClick={() => setFilter('toutes')}
            className={`px-4 py-2 rounded ${filter === 'toutes' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
          >
            Toutes ({attributions.length})
          </button>
          <button
            onClick={() => setFilter('historique')}
            className={`px-4 py-2 rounded ${filter === 'historique' ? 'bg-gray-600 text-white' : 'bg-gray-200'}`}
          >
            Historique ({attributions.filter(a => a.statut !== 'actif').length})
          </button>
        </div>
      </div>

      {/* Liste des attributions */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Personnel</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Badge</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type Badge</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date Attribution</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Statut</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {filteredAttributions().map((attribution) => (
              <tr key={attribution.id_attribution}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {attribution.nom} {attribution.prenom}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {attribution.type_personnel}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                  {attribution.epc_code}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {attribution.nom_type_badge}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {new Date(attribution.date_attribution).toLocaleDateString('fr-FR')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    attribution.statut === 'actif' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {attribution.statut === 'actif' ? 'Actif' : 'Désactivé'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  {attribution.statut === 'actif' && (
                    <button
                      onClick={() => handleCloturerAttribution(attribution.id_attribution)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Clôturer
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Modal de création */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg shadow-lg w-96">
            <h3 className="text-lg font-medium mb-4">Nouvelle Attribution</h3>
            <form onSubmit={handleCreateAttribution}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Personnel
                </label>
                <select
                  value={newAttribution.id_personnel}
                  onChange={(e) => setNewAttribution({...newAttribution, id_personnel: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                >
                  <option value="">Sélectionner un personnel</option>
                  {personnels.map((personnel) => (
                    <option key={personnel.id_personnel} value={personnel.id_personnel}>
                      {personnel.nom} {personnel.prenom} ({personnel.nom_type})
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mode d'attribution
                </label>
                <select
                  value={newAttribution.mode}
                  onChange={(e) => setNewAttribution({...newAttribution, mode: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="manuel">Manuel</option>
                  <option value="automatique">Automatique</option>
                </select>
              </div>

              {newAttribution.mode === 'manuel' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Badge
                  </label>
                  <select
                    value={newAttribution.id_badge}
                    onChange={(e) => setNewAttribution({...newAttribution, id_badge: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    required={newAttribution.mode === 'manuel'}
                  >
                    <option value="">Sélectionner un badge</option>
                    {badgesDisponibles.map((badge) => (
                      <option key={badge.id_badge} value={badge.id_badge}>
                        {badge.epc_code} ({badge.nom_type_badge})
                      </option>
                    ))}
                  </select>
                </div>
              )}

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Créer Attribution
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default AttributionManagement;
