-- ========== Types ==========
CREATE TABLE Type_Personnel (
    id_type_personnel SERIAL PRIMARY KEY,
    nom_type VARCHAR(50) NOT NULL
);

CREATE TABLE Grade (
    id_grade SERIAL PRIMARY KEY,
    nom_grade VARCHAR(50) NOT NULL
);

CREATE TABLE Unite (
    id_unite SERIAL PRIMARY KEY,
    nom_unite VARCHAR(50) NOT NULL
);

CREATE TABLE Type_Badge (
    id_type_badge SERIAL PRIMARY KEY,
    nom_type_badge VARCHAR(50) NOT NULL
);

-- ========== Données principales ==========
CREATE TABLE Personnel (
    id_personnel SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    id_type_personnel INT REFERENCES Type_Personnel(id_type_personnel),
    id_grade INT REFERENCES Grade(id_grade),
    id_unite INT REFERENCES Unite(id_unite)
);

CREATE TABLE Badge (
    id_badge SERIAL PRIMARY KEY,
    epc_code VARCHAR(32) UNIQUE NOT NULL,
    id_type_badge INT REFERENCES Type_Badge(id_type_badge)
);

CREATE TABLE Attribution_Badge (
    id_attribution SERIAL PRIMARY KEY,
    id_personnel INT REFERENCES Personnel(id_personnel),
    id_badge INT REFERENCES Badge(id_badge),
    date_attribution DATE NOT NULL,
    date_fin DATE,
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'désactivé', 'perdu'))
);

CREATE TABLE Porte (
    id_porte SERIAL PRIMARY KEY,
    libelle VARCHAR(50) NOT NULL
);

-- ========== Passage journalisé ==========
CREATE TABLE Passage (
    id_journal SERIAL PRIMARY KEY,
    id_badge INT REFERENCES Badge(id_badge),
    id_porte INT REFERENCES Porte(id_porte),
    date_acces TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    type_acces VARCHAR(10) NOT NULL,
    resultat VARCHAR(10) CHECK (resultat IN ('autorisé', 'refusé'))
);
