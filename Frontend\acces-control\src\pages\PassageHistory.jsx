import { useState, useEffect } from 'react';
import { passageAPI, referentielAPI } from '../services/api';

function PassageHistory() {
  const [passages, setPassages] = useState([]);
  const [passagesRecents, setPassagesRecents] = useState([]);
  const [statistiques, setStatistiques] = useState({});
  const [portes, setPortes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [filter, setFilter] = useState('recent'); // recent, all, autorises, refuses
  const [newPassage, setNewPassage] = useState({
    epc_code: '',
    id_porte: '',
    type_acces: 'entree'
  });

  useEffect(() => {
    loadData();
    // Actualiser les passages récents toutes les 30 secondes
    const interval = setInterval(loadPassagesRecents, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [passagesRes, recentsRes, statsRes, portesRes] = await Promise.all([
        passageAPI.getAllPassages({ limit: 100 }),
        passageAPI.getPassagesRecents(20),
        passageAPI.getStatistiquesPassages(),
        referentielAPI.getPortes()
      ]);
      
      setPassages(passagesRes.data);
      setPassagesRecents(recentsRes.data);
      setStatistiques(statsRes.data);
      setPortes(portesRes.data);
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadPassagesRecents = async () => {
    try {
      const recentsRes = await passageAPI.getPassagesRecents(20);
      setPassagesRecents(recentsRes.data);
    } catch (error) {
      console.error('Erreur lors du chargement des passages récents:', error);
    }
  };

  const handleCreatePassage = async (e) => {
    e.preventDefault();
    try {
      await passageAPI.createPassage({
        ...newPassage,
        date_acces: new Date().toISOString()
      });
      setNewPassage({ epc_code: '', id_porte: '', type_acces: 'entree' });
      setShowCreateForm(false);
      loadData();
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      alert('Erreur lors de l\'enregistrement du passage');
    }
  };

  const filteredPassages = () => {
    if (filter === 'recent') return passagesRecents;
    if (filter === 'autorises') return passages.filter(p => p.resultat === 'autorisé');
    if (filter === 'refuses') return passages.filter(p => p.resultat === 'refusé');
    return passages;
  };

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString('fr-FR');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Chargement...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Historique des Passages</h2>
        <div className="flex space-x-3">
          <button
            onClick={loadPassagesRecents}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
          >
            ↻ Actualiser
          </button>
          <button
            onClick={() => setShowCreateForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            + Simuler Passage
          </button>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-blue-600">{statistiques.total_passages || 0}</div>
          <div className="text-gray-600">Total Passages</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">{statistiques.passages_autorises || 0}</div>
          <div className="text-gray-600">Autorisés</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-red-600">{statistiques.passages_refuses || 0}</div>
          <div className="text-gray-600">Refusés</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-purple-600">{statistiques.badges_utilises || 0}</div>
          <div className="text-gray-600">Badges Utilisés</div>
        </div>
      </div>

      {/* Filtres */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex space-x-4">
          <button
            onClick={() => setFilter('recent')}
            className={`px-4 py-2 rounded ${filter === 'recent' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
          >
            Récents ({passagesRecents.length})
          </button>
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded ${filter === 'all' ? 'bg-gray-600 text-white' : 'bg-gray-200'}`}
          >
            Tous ({passages.length})
          </button>
          <button
            onClick={() => setFilter('autorises')}
            className={`px-4 py-2 rounded ${filter === 'autorises' ? 'bg-green-600 text-white' : 'bg-gray-200'}`}
          >
            Autorisés ({passages.filter(p => p.resultat === 'autorisé').length})
          </button>
          <button
            onClick={() => setFilter('refuses')}
            className={`px-4 py-2 rounded ${filter === 'refuses' ? 'bg-red-600 text-white' : 'bg-gray-200'}`}
          >
            Refusés ({passages.filter(p => p.resultat === 'refusé').length})
          </button>
        </div>
      </div>

      {/* Liste des passages */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium">
            {filter === 'recent' && '🔴 Temps Réel - Derniers Passages'}
            {filter === 'all' && 'Tous les Passages'}
            {filter === 'autorises' && '✅ Passages Autorisés'}
            {filter === 'refuses' && '❌ Passages Refusés'}
          </h3>
        </div>
        
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date/Heure</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Personnel</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Badge</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Porte</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Résultat</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {filteredPassages().map((passage) => (
              <tr key={passage.id_journal} className={filter === 'recent' ? 'hover:bg-blue-50' : ''}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatDateTime(passage.date_acces)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {passage.nom && passage.prenom ? `${passage.nom} ${passage.prenom}` : '-'}
                  </div>
                  <div className="text-sm text-gray-500">
                    {passage.type_personnel || '-'}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                  {passage.epc_code || 'Badge inconnu'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {passage.nom_porte || `Porte ${passage.id_porte}`}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    passage.type_acces === 'entree' 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-orange-100 text-orange-800'
                  }`}>
                    {passage.type_acces === 'entree' ? '→ Entrée' : '← Sortie'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    passage.resultat === 'autorisé' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {passage.resultat === 'autorisé' ? '✅ Autorisé' : '❌ Refusé'}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Modal de simulation de passage */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg shadow-lg w-96">
            <h3 className="text-lg font-medium mb-4">Simuler un Passage</h3>
            <form onSubmit={handleCreatePassage}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Code EPC du Badge
                </label>
                <input
                  type="text"
                  value={newPassage.epc_code}
                  onChange={(e) => setNewPassage({...newPassage, epc_code: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="E200001617501001"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Porte
                </label>
                <select
                  value={newPassage.id_porte}
                  onChange={(e) => setNewPassage({...newPassage, id_porte: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                >
                  <option value="">Sélectionner une porte</option>
                  {portes.map((porte) => (
                    <option key={porte.id_porte} value={porte.id_porte}>
                      {porte.libelle}
                    </option>
                  ))}
                </select>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Type d'accès
                </label>
                <select
                  value={newPassage.type_acces}
                  onChange={(e) => setNewPassage({...newPassage, type_acces: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="entree">Entrée</option>
                  <option value="sortie">Sortie</option>
                </select>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Simuler Passage
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default PassageHistory;
