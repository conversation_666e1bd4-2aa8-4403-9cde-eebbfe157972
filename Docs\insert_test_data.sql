-- ========== DONNÉES DE TEST - SYSTÈME DE CONTRÔLE D'ACCÈS ==========

-- Vider les tables dans l'ordre des dépendances
DELETE FROM passage;
DELETE FROM attribution_badge;
DELETE FROM personnel;
DELETE FROM badge;
DELETE FROM type_personnel;
DELETE FROM type_badge;
DELETE FROM grade;
DELETE FROM unite;
DELETE FROM porte;

-- Réinitialiser les séquences
ALTER SEQUENCE type_personnel_id_type_personnel_seq RESTART WITH 1;
ALTER SEQUENCE type_badge_id_type_badge_seq RESTART WITH 1;
ALTER SEQUENCE grade_id_grade_seq RESTART WITH 1;
ALTER SEQUENCE unite_id_unite_seq RESTART WITH 1;
ALTER SEQUENCE porte_id_porte_seq RESTART WITH 1;
ALTER SEQUENCE personnel_id_personnel_seq RESTART WITH 1;
ALTER SEQUENCE badge_id_badge_seq RESTART WITH 1;
ALTER SEQUENCE attribution_badge_id_attribution_seq RESTART WITH 1;
ALTER SEQUENCE passage_id_journal_seq RESTART WITH 1;

-- ========== RÉFÉRENTIELS ==========

-- Types de Personnel
INSERT INTO type_personnel (nom_type) VALUES 
('Interne'),
('Externe'),
('Militaire'),
('Civil');

-- Types de Badge
INSERT INTO type_badge (nom_type_badge) VALUES 
('Badge Interne'),
('Badge Externe'),
('Badge Militaire'),
('Badge Civil');

-- Grades
INSERT INTO grade (nom_grade) VALUES 
('Capitaine'),
('Lieutenant'),
('Sergent'),
('Caporal'),
('Soldat'),
('Colonel'),
('Commandant'),
('Adjudant'),
('Ingénieur'),
('Technicien');

-- Unités
INSERT INTO unite (nom_unite) VALUES 
('1er Régiment d''Infanterie'),
('2ème Régiment de Cavalerie'),
('3ème Régiment d''Artillerie'),
('État-Major'),
('Service Technique'),
('Maintenance'),
('Logistique'),
('Sécurité');

-- Portes
INSERT INTO porte (libelle) VALUES 
('Entrée Principale'),
('Sortie Principale'),
('Accès Parking'),
('Entrée Service'),
('Accès Technique'),
('Salle de Réunion A'),
('Salle de Réunion B'),
('Bureau Direction'),
('Armurerie'),
('Infirmerie');

-- ========== BADGES ==========

-- Badges Internes (type 1)
INSERT INTO badge (epc_code, id_type_badge) VALUES 
('E200001617501001', 1),
('E200001617501002', 1),
('E200001617501003', 1),
('E200001617501004', 1),
('E200001617501005', 1),
('E200001617501006', 1),
('E200001617501007', 1),
('E200001617501008', 1);

-- Badges Externes (type 2)
INSERT INTO badge (epc_code, id_type_badge) VALUES 
('E200001617502001', 2),
('E200001617502002', 2),
('E200001617502003', 2),
('E200001617502004', 2);

-- Badges Militaires (type 3)
INSERT INTO badge (epc_code, id_type_badge) VALUES 
('E200001617503001', 3),
('E200001617503002', 3),
('E200001617503003', 3);

-- Badges Civils (type 4)
INSERT INTO badge (epc_code, id_type_badge) VALUES 
('E200001617504001', 4),
('E200001617504002', 4);

-- ========== PERSONNEL ==========

-- Personnel Interne (type 1)
INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES 
('Dupont', 'Jean', 1, 1, 1),
('Martin', 'Pierre', 1, 2, 1),
('Durand', 'Marie', 1, 3, 2),
('Moreau', 'Paul', 1, 4, 2),
('Leroy', 'Sophie', 1, 9, 5),
('Bernard', 'Luc', 1, 10, 6);

-- Personnel Externe (type 2)
INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES 
('Contractor', 'John', 2, 9, 5),
('Externe', 'Alice', 2, 10, 6);

-- Personnel Militaire (type 3)
INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES 
('Colonel', 'Robert', 3, 6, 4),
('Commandant', 'Michel', 3, 7, 4),
('Adjudant', 'François', 3, 8, 3);

-- Personnel Civil (type 4)
INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES 
('Civil', 'Emma', 4, 9, 7),
('Technicien', 'David', 4, 10, 8);

-- ========== ATTRIBUTIONS DE BADGES ==========

-- Attributions actives pour personnel interne
INSERT INTO attribution_badge (id_personnel, id_badge, date_attribution, statut) VALUES 
(1, 1, '2025-01-01', 'actif'),    -- Dupont Jean
(2, 2, '2025-01-02', 'actif'),    -- Martin Pierre
(3, 3, '2025-01-03', 'actif'),    -- Durand Marie
(4, 4, '2025-01-04', 'actif'),    -- Moreau Paul
(5, 5, '2025-01-05', 'actif'),    -- Leroy Sophie
(6, 6, '2025-01-06', 'actif');    -- Bernard Luc

-- Attributions actives pour personnel externe
INSERT INTO attribution_badge (id_personnel, id_badge, date_attribution, statut) VALUES 
(7, 9, '2025-01-10', 'actif'),    -- Contractor John
(8, 10, '2025-01-11', 'actif');   -- Externe Alice

-- Attributions actives pour personnel militaire
INSERT INTO attribution_badge (id_personnel, id_badge, date_attribution, statut) VALUES 
(9, 13, '2025-01-15', 'actif'),   -- Colonel Robert
(10, 14, '2025-01-16', 'actif'),  -- Commandant Michel
(11, 15, '2025-01-17', 'actif');  -- Adjudant François

-- Attributions actives pour personnel civil
INSERT INTO attribution_badge (id_personnel, id_badge, date_attribution, statut) VALUES 
(12, 16, '2025-01-20', 'actif'),  -- Civil Emma
(13, 17, '2025-01-21', 'actif');  -- Technicien David

-- Quelques attributions historiques (désactivées)
INSERT INTO attribution_badge (id_personnel, id_badge, date_attribution, date_fin, statut) VALUES 
(1, 7, '2024-06-01', '2024-12-31', 'désactivé'),  -- Ancien badge de Dupont
(2, 8, '2024-07-01', '2024-12-31', 'désactivé');  -- Ancien badge de Martin

-- ========== PASSAGES ==========

-- Passages récents (dernières 24h simulées)
INSERT INTO passage (id_badge, id_porte, date_acces, type_acces, resultat) VALUES 
-- Passages autorisés
(1, 1, '2025-07-07 08:00:00', 'entree', 'autorisé'),
(2, 1, '2025-07-07 08:15:00', 'entree', 'autorisé'),
(3, 1, '2025-07-07 08:30:00', 'entree', 'autorisé'),
(1, 6, '2025-07-07 09:00:00', 'entree', 'autorisé'),
(2, 7, '2025-07-07 09:30:00', 'entree', 'autorisé'),
(4, 1, '2025-07-07 10:00:00', 'entree', 'autorisé'),
(5, 1, '2025-07-07 10:15:00', 'entree', 'autorisé'),
(13, 8, '2025-07-07 11:00:00', 'entree', 'autorisé'),
(14, 4, '2025-07-07 11:30:00', 'entree', 'autorisé'),
(1, 2, '2025-07-07 12:00:00', 'sortie', 'autorisé'),
(2, 2, '2025-07-07 12:15:00', 'sortie', 'autorisé'),
(1, 1, '2025-07-07 13:00:00', 'entree', 'autorisé'),
(2, 1, '2025-07-07 13:15:00', 'entree', 'autorisé'),
(9, 1, '2025-07-07 14:00:00', 'entree', 'autorisé'),
(10, 1, '2025-07-07 14:30:00', 'entree', 'autorisé'),
(16, 5, '2025-07-07 15:00:00', 'entree', 'autorisé'),
(17, 6, '2025-07-07 15:30:00', 'entree', 'autorisé'),
(1, 2, '2025-07-07 17:00:00', 'sortie', 'autorisé'),
(2, 2, '2025-07-07 17:15:00', 'sortie', 'autorisé'),
(3, 2, '2025-07-07 17:30:00', 'sortie', 'autorisé');

-- Quelques passages refusés (badges inconnus ou inactifs)
INSERT INTO passage (id_badge, id_porte, date_acces, type_acces, resultat) VALUES 
(NULL, 1, '2025-07-07 08:45:00', 'entree', 'refusé'),  -- Badge inconnu
(NULL, 1, '2025-07-07 12:30:00', 'entree', 'refusé'),  -- Badge inconnu
(NULL, 2, '2025-07-07 16:00:00', 'sortie', 'refusé');  -- Badge inconnu

-- Passages historiques (simulation sur plusieurs jours)
INSERT INTO passage (id_badge, id_porte, date_acces, type_acces, resultat) VALUES 
(1, 1, '2025-07-06 08:00:00', 'entree', 'autorisé'),
(1, 2, '2025-07-06 17:00:00', 'sortie', 'autorisé'),
(2, 1, '2025-07-06 08:30:00', 'entree', 'autorisé'),
(2, 2, '2025-07-06 17:30:00', 'sortie', 'autorisé'),
(3, 1, '2025-07-05 09:00:00', 'entree', 'autorisé'),
(3, 2, '2025-07-05 18:00:00', 'sortie', 'autorisé'),
(4, 1, '2025-07-05 08:15:00', 'entree', 'autorisé'),
(4, 2, '2025-07-05 17:15:00', 'sortie', 'autorisé');

-- ========== AFFICHAGE DES STATISTIQUES ==========

-- Compter les données insérées
SELECT 'Types Personnel' as table_name, COUNT(*) as count FROM type_personnel
UNION ALL
SELECT 'Types Badge', COUNT(*) FROM type_badge
UNION ALL
SELECT 'Grades', COUNT(*) FROM grade
UNION ALL
SELECT 'Unités', COUNT(*) FROM unite
UNION ALL
SELECT 'Portes', COUNT(*) FROM porte
UNION ALL
SELECT 'Badges', COUNT(*) FROM badge
UNION ALL
SELECT 'Personnel', COUNT(*) FROM personnel
UNION ALL
SELECT 'Attributions', COUNT(*) FROM attribution_badge
UNION ALL
SELECT 'Passages', COUNT(*) FROM passage;

-- Afficher les attributions actives
SELECT 
    p.nom, p.prenom, 
    tp.nom_type as type_personnel,
    b.epc_code,
    tb.nom_type_badge,
    ab.date_attribution
FROM attribution_badge ab
JOIN personnel p ON ab.id_personnel = p.id_personnel
JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
JOIN badge b ON ab.id_badge = b.id_badge
JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
WHERE ab.statut = 'actif'
ORDER BY ab.date_attribution;
