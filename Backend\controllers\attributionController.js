const db = require('../models/db');

// ========== ATTRIBUTION DES BADGES ==========

// Attribuer un badge à un personnel (manuel ou automatique)
const createAttribution = async (req, res) => {
  const { id_personnel, id_badge, mode = 'manuel' } = req.body;
  const client = await db.connect();
  
  try {
    await client.query('BEGIN');
    
    // Vérifier que le personnel existe
    const personnelCheck = await client.query('SELECT * FROM personnel WHERE id_personnel = $1', [id_personnel]);
    if (personnelCheck.rows.length === 0) {
      throw new Error('Personnel non trouvé');
    }
    
    const personnel = personnelCheck.rows[0];
    let badge;
    
    if (mode === 'automatique') {
      // Attribution automatique : trouver un badge libre du bon type
      const typePersonnel = personnel.id_type_personnel;
      let typeBadge;
      
      // Mapper type personnel -> type badge
      switch (typePersonnel) {
        case 1: typeBadge = 1; break; // Interne -> Badge interne
        case 2: typeBadge = 2; break; // Externe -> Badge externe
        case 3: typeBadge = 3; break; // Militaire -> Badge militaire
        case 4: typeBadge = 4; break; // Civil -> Badge civil
        default: throw new Error('Type de personnel non reconnu');
      }
      
      const badgeResult = await client.query(`
        SELECT b.* FROM badge b 
        LEFT JOIN attribution_badge ab ON b.id_badge = ab.id_badge AND ab.statut = 'actif'
        WHERE b.id_type_badge = $1 AND ab.id_badge IS NULL 
        LIMIT 1
      `, [typeBadge]);
      
      if (badgeResult.rows.length === 0) {
        throw new Error(`Aucun badge de type ${typeBadge} disponible`);
      }
      
      badge = badgeResult.rows[0];
      
    } else {
      // Attribution manuelle : vérifier que le badge spécifié est disponible
      if (!id_badge) {
        throw new Error('ID badge requis pour attribution manuelle');
      }
      
      const badgeCheck = await client.query(`
        SELECT b.* FROM badge b 
        LEFT JOIN attribution_badge ab ON b.id_badge = ab.id_badge AND ab.statut = 'actif'
        WHERE b.id_badge = $1 AND ab.id_badge IS NULL
      `, [id_badge]);
      
      if (badgeCheck.rows.length === 0) {
        throw new Error('Badge non disponible ou inexistant');
      }
      
      badge = badgeCheck.rows[0];
    }
    
    // Vérifier que le personnel n'a pas déjà un badge actif
    const activeAttribution = await client.query(
      'SELECT * FROM attribution_badge WHERE id_personnel = $1 AND statut = $2',
      [id_personnel, 'actif']
    );
    
    if (activeAttribution.rows.length > 0) {
      throw new Error('Ce personnel a déjà un badge actif');
    }
    
    // Créer l'attribution
    const attributionResult = await client.query(
      'INSERT INTO attribution_badge (id_personnel, id_badge, date_attribution, statut) VALUES ($1, $2, CURRENT_DATE, $3) RETURNING *',
      [id_personnel, badge.id_badge, 'actif']
    );
    
    await client.query('COMMIT');
    
    res.status(201).json({
      attribution: attributionResult.rows[0],
      personnel,
      badge,
      mode,
      message: `Badge ${mode === 'automatique' ? 'automatiquement' : 'manuellement'} attribué`
    });
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Erreur attribution badge :', error);
    res.status(500).json({ error: error.message || 'Erreur lors de l\'attribution' });
  } finally {
    client.release();
  }
};

// Clôturer une attribution (date de fin + statut inactif)
const cloturerAttribution = async (req, res) => {
  const { id } = req.params;
  const { motif = 'Clôture manuelle' } = req.body;
  
  try {
    const result = await db.query(
      'UPDATE attribution_badge SET statut = $1, date_fin = CURRENT_DATE WHERE id_attribution = $2 AND statut = $3 RETURNING *',
      ['désactivé', id, 'actif']
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Attribution active non trouvée' });
    }
    
    res.status(200).json({
      attribution: result.rows[0],
      motif,
      message: 'Attribution clôturée avec succès'
    });
  } catch (error) {
    console.error('❌ Erreur clôture attribution :', error);
    res.status(500).json({ error: 'Erreur lors de la clôture' });
  }
};

// Lister toutes les attributions
const getAllAttributions = async (req, res) => {
  const { statut, page = 1, limit = 50 } = req.query;
  const offset = (page - 1) * limit;
  
  try {
    let query = `
      SELECT ab.*, 
             p.nom, p.prenom, tp.nom_type as type_personnel,
             b.epc_code, tb.nom_type_badge
      FROM attribution_badge ab
      JOIN personnel p ON ab.id_personnel = p.id_personnel
      JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
      JOIN badge b ON ab.id_badge = b.id_badge
      JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
    `;
    
    const params = [];
    if (statut) {
      query += ' WHERE ab.statut = $1';
      params.push(statut);
    }
    
    query += ' ORDER BY ab.date_attribution DESC LIMIT $' + (params.length + 1) + ' OFFSET $' + (params.length + 2);
    params.push(limit, offset);
    
    const result = await db.query(query, params);
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération attributions :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Détails d'une attribution
const getAttributionById = async (req, res) => {
  const { id } = req.params;
  
  try {
    const result = await db.query(`
      SELECT ab.*, 
             p.nom, p.prenom, tp.nom_type as type_personnel, g.nom_grade, u.nom_unite,
             b.epc_code, tb.nom_type_badge
      FROM attribution_badge ab
      JOIN personnel p ON ab.id_personnel = p.id_personnel
      JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
      LEFT JOIN grade g ON p.id_grade = g.id_grade
      LEFT JOIN unite u ON p.id_unite = u.id_unite
      JOIN badge b ON ab.id_badge = b.id_badge
      JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
      WHERE ab.id_attribution = $1
    `, [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Attribution non trouvée' });
    }
    
    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur récupération attribution :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Lister les attributions actives
const getAttributionsActives = async (req, res) => {
  try {
    const result = await db.query(`
      SELECT ab.*, 
             p.nom, p.prenom, tp.nom_type as type_personnel,
             b.epc_code, tb.nom_type_badge
      FROM attribution_badge ab
      JOIN personnel p ON ab.id_personnel = p.id_personnel
      JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
      JOIN badge b ON ab.id_badge = b.id_badge
      JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
      WHERE ab.statut = 'actif'
      ORDER BY ab.date_attribution DESC
    `);
    
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération attributions actives :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Historique des badges d'un personnel
const getHistoriquePersonnel = async (req, res) => {
  const { id_personnel } = req.params;
  
  try {
    const result = await db.query(`
      SELECT ab.*, 
             b.epc_code, tb.nom_type_badge
      FROM attribution_badge ab
      JOIN badge b ON ab.id_badge = b.id_badge
      JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
      WHERE ab.id_personnel = $1
      ORDER BY ab.date_attribution DESC
    `, [id_personnel]);
    
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération historique personnel :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

module.exports = {
  createAttribution,
  cloturerAttribution,
  getAllAttributions,
  getAttributionById,
  getAttributionsActives,
  getHistoriquePersonnel,
};
