const db = require('../models/db');

// ========== GESTION DES BADGES ==========

// Créer un badge avec type (interne/externe/militaire/civil)
const createBadge = async (req, res) => {
  const { epc_code, id_type_badge } = req.body;
  
  try {
    // Vérifier que l'EPC n'existe pas déjà
    const existingBadge = await db.query('SELECT * FROM badge WHERE epc_code = $1', [epc_code]);
    if (existingBadge.rows.length > 0) {
      return res.status(400).json({ error: 'Un badge avec ce code EPC existe déjà' });
    }
    
    const result = await db.query(
      'INSERT INTO badge (epc_code, id_type_badge) VALUES ($1, $2) RETURNING *',
      [epc_code, id_type_badge]
    );
    
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur création badge :', error);
    res.status(500).json({ error: 'Erreur lors de la création du badge' });
  }
};

// Lister tous les badges
const getAllBadges = async (req, res) => {
  try {
    const result = await db.query(`
      SELECT b.*, tb.nom_type_badge,
             CASE 
               WHEN ab.id_badge IS NOT NULL AND ab.statut = 'actif' THEN 'attribué'
               ELSE 'libre'
             END as statut_attribution,
             p.nom, p.prenom, ab.date_attribution
      FROM badge b
      LEFT JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
      LEFT JOIN attribution_badge ab ON b.id_badge = ab.id_badge AND ab.statut = 'actif'
      LEFT JOIN personnel p ON ab.id_personnel = p.id_personnel
      ORDER BY b.id_badge
    `);
    
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération badges :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Lister les badges non attribués (libres)
const getBadgesDisponibles = async (req, res) => {
  const { type_badge } = req.query; // Optionnel : filtrer par type
  
  try {
    let query = `
      SELECT b.*, tb.nom_type_badge
      FROM badge b
      LEFT JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
      LEFT JOIN attribution_badge ab ON b.id_badge = ab.id_badge AND ab.statut = 'actif'
      WHERE ab.id_badge IS NULL
    `;
    
    const params = [];
    if (type_badge) {
      query += ' AND b.id_type_badge = $1';
      params.push(type_badge);
    }
    
    query += ' ORDER BY b.id_badge';
    
    const result = await db.query(query, params);
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération badges disponibles :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Détails d'un badge
const getBadgeById = async (req, res) => {
  const { id } = req.params;
  
  try {
    const result = await db.query(`
      SELECT b.*, tb.nom_type_badge,
             CASE 
               WHEN ab.id_badge IS NOT NULL AND ab.statut = 'actif' THEN 'attribué'
               ELSE 'libre'
             END as statut_attribution,
             p.nom, p.prenom, p.id_personnel, ab.date_attribution, ab.statut
      FROM badge b
      LEFT JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
      LEFT JOIN attribution_badge ab ON b.id_badge = ab.id_badge AND ab.statut = 'actif'
      LEFT JOIN personnel p ON ab.id_personnel = p.id_personnel
      WHERE b.id_badge = $1
    `, [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Badge non trouvé' });
    }
    
    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur récupération badge :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Modifier un badge
const updateBadge = async (req, res) => {
  const { id } = req.params;
  const { epc_code, id_type_badge } = req.body;
  
  try {
    // Vérifier que l'EPC n'existe pas déjà pour un autre badge
    if (epc_code) {
      const existingBadge = await db.query('SELECT * FROM badge WHERE epc_code = $1 AND id_badge != $2', [epc_code, id]);
      if (existingBadge.rows.length > 0) {
        return res.status(400).json({ error: 'Un autre badge avec ce code EPC existe déjà' });
      }
    }
    
    const result = await db.query(
      'UPDATE badge SET epc_code = COALESCE($1, epc_code), id_type_badge = COALESCE($2, id_type_badge) WHERE id_badge = $3 RETURNING *',
      [epc_code, id_type_badge, id]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Badge non trouvé' });
    }
    
    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur modification badge :', error);
    res.status(500).json({ error: 'Erreur lors de la modification' });
  }
};

// Supprimer un badge (s'il n'est pas actif)
const deleteBadge = async (req, res) => {
  const { id } = req.params;
  
  try {
    // Vérifier que le badge n'est pas actuellement attribué
    const activeAttribution = await db.query(
      'SELECT * FROM attribution_badge WHERE id_badge = $1 AND statut = $2',
      [id, 'actif']
    );
    
    if (activeAttribution.rows.length > 0) {
      return res.status(400).json({ 
        error: 'Impossible de supprimer un badge actuellement attribué',
        attribution_active: activeAttribution.rows[0]
      });
    }
    
    const result = await db.query('DELETE FROM badge WHERE id_badge = $1 RETURNING *', [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Badge non trouvé' });
    }
    
    res.status(200).json({ 
      message: 'Badge supprimé avec succès', 
      badge: result.rows[0] 
    });
  } catch (error) {
    console.error('❌ Erreur suppression badge :', error);
    res.status(500).json({ error: 'Erreur lors de la suppression' });
  }
};

module.exports = {
  createBadge,
  getAllBadges,
  getBadgesDisponibles,
  getBadgeById,
  updateBadge,
  deleteBadge,
};
