const express = require('express');
const router = express.Router();
const { 
  createAttribution,
  cloturerAttribution,
  getAllAttributions,
  getAttributionById,
  getAttributionsActives,
  getHistoriquePersonnel
} = require('../controllers/attributionController');

// ========== ROUTES ATTRIBUTION DES BADGES ==========

// POST /api/attributions - Attribuer un badge à un personnel (manuel ou automatique)
router.post('/', createAttribution);

// PUT /api/attributions/:id/fin - Clôturer une attribution (date de fin + statut inactif)
router.put('/:id/fin', cloturerAttribution);

// GET /api/attributions/actives - Lister les attributions actives (DOIT ÊTRE AVANT /:id)
router.get('/actives', getAttributionsActives);

// GET /api/attributions - Lister toutes les attributions
router.get('/', getAllAttributions);

// GET /api/attributions/:id - Détails d'une attribution
router.get('/:id', getAttributionById);

// GET /api/attributions/personnel/:id_personnel - Historique des badges d'un personnel
router.get('/personnel/:id_personnel', getHistoriquePersonnel);

module.exports = router;
