const express = require('express');
const router = express.Router();
const {
  getAllPersonnels,
  createPersonnel,
  createPersonnelInterne,
  getPersonnelsInternes,
  getPersonnelInterneById,
  updatePersonnelInterne,
  deletePersonnelInterne,
  createPersonnelExterne,
  getPersonnelsExternes,
  getPersonnelExterneById,
  updatePersonnelExterne,
  deletePersonnelExterne
} = require('../controllers/personnelController');

// ========== ROUTES GÉNÉRALES (compatibilité) ==========
// GET /api/personnels
router.get('/', getAllPersonnels);

// POST /api/personnels
router.post('/', createPersonnel);

// ========== ROUTES PERSONNEL INTERNE ==========
// POST /api/personnels/internes - Créer un personnel interne avec badge automatiquement attribué
router.post('/internes', createPersonnelInterne);

// GET /api/personnels/internes - Liste des personnels internes
router.get('/internes', getPersonnelsInternes);

// GET /api/personnels/internes/:id - Détails d'un personnel interne
router.get('/internes/:id', getPersonnelInterneById);

// PUT /api/personnels/internes/:id - Modifier un personnel interne
router.put('/internes/:id', updatePersonnelInterne);

// DELETE /api/personnels/internes/:id - Supprimer un personnel interne (logique ou physique)
router.delete('/internes/:id', deletePersonnelInterne);

// ========== ROUTES PERSONNEL EXTERNE ==========
// POST /api/personnels/externes - Créer un personnel externe (badge sélectionné manuellement)
router.post('/externes', createPersonnelExterne);

// GET /api/personnels/externes - Liste des personnels externes
router.get('/externes', getPersonnelsExternes);

// GET /api/personnels/externes/:id - Détails d'un personnel externe
router.get('/externes/:id', getPersonnelExterneById);

// PUT /api/personnels/externes/:id - Modifier un personnel externe
router.put('/externes/:id', updatePersonnelExterne);

// DELETE /api/personnels/externes/:id - Supprimer un personnel externe
router.delete('/externes/:id', deletePersonnelExterne);

module.exports = router;
