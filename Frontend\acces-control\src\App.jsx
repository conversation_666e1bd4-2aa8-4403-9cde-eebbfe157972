import { useState, useEffect } from 'react';
import Sidebar from './components/Sidebar';
import Dashboard from './pages/Dashboard';
import PersonnelList from './pages/PersonnelList';
import BadgeManagement from './pages/BadgeManagement';
import AttributionManagement from './pages/AttributionManagement';
import PassageHistory from './pages/PassageHistory';
import ReferentielManagement from './pages/ReferentielManagement';
import { utilAPI } from './services/api';

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [apiStatus, setApiStatus] = useState('checking');

  useEffect(() => {
    checkApiStatus();
  }, []);

  const checkApiStatus = async () => {
    try {
      await utilAPI.ping();
      setApiStatus('connected');
    } catch (error) {
      setApiStatus('disconnected');
      console.error('API non disponible:', error);
    }
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'personnel':
        return <PersonnelList />;
      case 'badges':
        return <BadgeManagement />;
      case 'attributions':
        return <AttributionManagement />;
      case 'passages':
        return <PassageHistory />;
      case 'referentiels':
        return <ReferentielManagement />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        apiStatus={apiStatus}
        onRefreshApi={checkApiStatus}
      />

      <main className="flex-1 overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold text-gray-900">
                Système de Contrôle d'Accès
              </h1>

              <div className="flex items-center space-x-4">
                {/* Status API */}
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${
                    apiStatus === 'connected' ? 'bg-green-500' :
                    apiStatus === 'disconnected' ? 'bg-red-500' : 'bg-yellow-500'
                  }`}></div>
                  <span className="text-sm text-gray-600">
                    API {apiStatus === 'connected' ? 'Connectée' :
                         apiStatus === 'disconnected' ? 'Déconnectée' : 'Vérification...'}
                  </span>
                  <button
                    onClick={checkApiStatus}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    ↻
                  </button>
                </div>

                <div className="text-sm text-gray-500">
                  {new Date().toLocaleString('fr-FR')}
                </div>
              </div>
            </div>
          </div>

          {/* Contenu principal */}
          {apiStatus === 'disconnected' ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
              <div className="text-red-600 text-lg font-semibold mb-2">
                ⚠️ API Backend Non Disponible
              </div>
              <p className="text-red-700 mb-4">
                Impossible de se connecter au serveur backend. Vérifiez que le serveur est démarré sur http://localhost:3000
              </p>
              <button
                onClick={checkApiStatus}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
              >
                Réessayer la connexion
              </button>
            </div>
          ) : (
            renderPage()
          )}
        </div>
      </main>
    </div>
  );
}

export default App;
