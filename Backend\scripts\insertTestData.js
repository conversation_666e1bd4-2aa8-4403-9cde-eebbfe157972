const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function insertTestData() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Début de l\'insertion des données de test...');
    
    // Vider les tables dans l'ordre des dépendances
    console.log('🗑️ Nettoyage des tables existantes...');
    await client.query('DELETE FROM passage');
    await client.query('DELETE FROM attribution_badge');
    await client.query('DELETE FROM personnel');
    await client.query('DELETE FROM badge');
    await client.query('DELETE FROM type_personnel');
    await client.query('DELETE FROM type_badge');
    await client.query('DELETE FROM grade');
    await client.query('DELETE FROM unite');
    await client.query('DELETE FROM porte');

    // Réinitialiser les séquences
    console.log('🔄 Réinitialisation des séquences...');
    await client.query('ALTER SEQUENCE type_personnel_id_type_personnel_seq RESTART WITH 1');
    await client.query('ALTER SEQUENCE type_badge_id_type_badge_seq RESTART WITH 1');
    await client.query('ALTER SEQUENCE grade_id_grade_seq RESTART WITH 1');
    await client.query('ALTER SEQUENCE unite_id_unite_seq RESTART WITH 1');
    await client.query('ALTER SEQUENCE porte_id_porte_seq RESTART WITH 1');
    await client.query('ALTER SEQUENCE personnel_id_personnel_seq RESTART WITH 1');
    await client.query('ALTER SEQUENCE badge_id_badge_seq RESTART WITH 1');
    await client.query('ALTER SEQUENCE attribution_badge_id_attribution_seq RESTART WITH 1');
    await client.query('ALTER SEQUENCE passage_id_journal_seq RESTART WITH 1');

    // Types de Personnel
    console.log('👥 Insertion des types de personnel...');
    await client.query(`
      INSERT INTO type_personnel (nom_type) VALUES 
      ('Interne'), ('Externe'), ('Militaire'), ('Civil')
    `);

    // Types de Badge
    console.log('🪪 Insertion des types de badge...');
    await client.query(`
      INSERT INTO type_badge (nom_type_badge) VALUES 
      ('Badge Interne'), ('Badge Externe'), ('Badge Militaire'), ('Badge Civil')
    `);

    // Grades
    console.log('🎖️ Insertion des grades...');
    await client.query(`
      INSERT INTO grade (nom_grade) VALUES 
      ('Capitaine'), ('Lieutenant'), ('Sergent'), ('Caporal'), ('Soldat'),
      ('Colonel'), ('Commandant'), ('Adjudant'), ('Ingénieur'), ('Technicien')
    `);

    // Unités
    console.log('🏢 Insertion des unités...');
    await client.query(`
      INSERT INTO unite (nom_unite) VALUES 
      ('1er Régiment d''Infanterie'), ('2ème Régiment de Cavalerie'), 
      ('3ème Régiment d''Artillerie'), ('État-Major'), ('Service Technique'),
      ('Maintenance'), ('Logistique'), ('Sécurité')
    `);

    // Portes
    console.log('🚪 Insertion des portes...');
    await client.query(`
      INSERT INTO porte (libelle) VALUES 
      ('Entrée Principale'), ('Sortie Principale'), ('Accès Parking'),
      ('Entrée Service'), ('Accès Technique'), ('Salle de Réunion A'),
      ('Salle de Réunion B'), ('Bureau Direction'), ('Armurerie'), ('Infirmerie')
    `);

    // Badges
    console.log('🏷️ Insertion des badges...');
    // Badges Internes
    await client.query(`
      INSERT INTO badge (epc_code, id_type_badge) VALUES 
      ('E200001617501001', 1), ('E200001617501002', 1), ('E200001617501003', 1),
      ('E200001617501004', 1), ('E200001617501005', 1), ('E200001617501006', 1),
      ('E200001617501007', 1), ('E200001617501008', 1)
    `);
    
    // Badges Externes
    await client.query(`
      INSERT INTO badge (epc_code, id_type_badge) VALUES 
      ('E200001617502001', 2), ('E200001617502002', 2), 
      ('E200001617502003', 2), ('E200001617502004', 2)
    `);
    
    // Badges Militaires
    await client.query(`
      INSERT INTO badge (epc_code, id_type_badge) VALUES 
      ('E200001617503001', 3), ('E200001617503002', 3), ('E200001617503003', 3)
    `);
    
    // Badges Civils
    await client.query(`
      INSERT INTO badge (epc_code, id_type_badge) VALUES 
      ('E200001617504001', 4), ('E200001617504002', 4)
    `);

    // Personnel
    console.log('👤 Insertion du personnel...');
    // Personnel Interne
    await client.query(`
      INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES 
      ('Dupont', 'Jean', 1, 1, 1), ('Martin', 'Pierre', 1, 2, 1),
      ('Durand', 'Marie', 1, 3, 2), ('Moreau', 'Paul', 1, 4, 2),
      ('Leroy', 'Sophie', 1, 9, 5), ('Bernard', 'Luc', 1, 10, 6)
    `);
    
    // Personnel Externe
    await client.query(`
      INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES 
      ('Contractor', 'John', 2, 9, 5), ('Externe', 'Alice', 2, 10, 6)
    `);
    
    // Personnel Militaire
    await client.query(`
      INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES 
      ('Colonel', 'Robert', 3, 6, 4), ('Commandant', 'Michel', 3, 7, 4),
      ('Adjudant', 'François', 3, 8, 3)
    `);
    
    // Personnel Civil
    await client.query(`
      INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES 
      ('Civil', 'Emma', 4, 9, 7), ('Technicien', 'David', 4, 10, 8)
    `);

    // Attributions de badges
    console.log('🔗 Insertion des attributions...');
    await client.query(`
      INSERT INTO attribution_badge (id_personnel, id_badge, date_attribution, statut) VALUES 
      (1, 1, '2025-01-01', 'actif'), (2, 2, '2025-01-02', 'actif'),
      (3, 3, '2025-01-03', 'actif'), (4, 4, '2025-01-04', 'actif'),
      (5, 5, '2025-01-05', 'actif'), (6, 6, '2025-01-06', 'actif'),
      (7, 9, '2025-01-10', 'actif'), (8, 10, '2025-01-11', 'actif'),
      (9, 13, '2025-01-15', 'actif'), (10, 14, '2025-01-16', 'actif'),
      (11, 15, '2025-01-17', 'actif'), (12, 16, '2025-01-20', 'actif'),
      (13, 17, '2025-01-21', 'actif')
    `);

    // Quelques attributions historiques
    await client.query(`
      INSERT INTO attribution_badge (id_personnel, id_badge, date_attribution, date_fin, statut) VALUES 
      (1, 7, '2024-06-01', '2024-12-31', 'désactivé'),
      (2, 8, '2024-07-01', '2024-12-31', 'désactivé')
    `);

    // Passages
    console.log('🚶 Insertion des passages...');
    await client.query(`
      INSERT INTO passage (id_badge, id_porte, date_acces, type_acces, resultat) VALUES 
      (1, 1, '2025-07-07 08:00:00', 'entree', 'autorisé'),
      (2, 1, '2025-07-07 08:15:00', 'entree', 'autorisé'),
      (3, 1, '2025-07-07 08:30:00', 'entree', 'autorisé'),
      (1, 6, '2025-07-07 09:00:00', 'entree', 'autorisé'),
      (2, 7, '2025-07-07 09:30:00', 'entree', 'autorisé'),
      (4, 1, '2025-07-07 10:00:00', 'entree', 'autorisé'),
      (5, 1, '2025-07-07 10:15:00', 'entree', 'autorisé'),
      (13, 8, '2025-07-07 11:00:00', 'entree', 'autorisé'),
      (14, 4, '2025-07-07 11:30:00', 'entree', 'autorisé'),
      (1, 2, '2025-07-07 12:00:00', 'sortie', 'autorisé'),
      (2, 2, '2025-07-07 12:15:00', 'sortie', 'autorisé'),
      (1, 1, '2025-07-07 13:00:00', 'entree', 'autorisé'),
      (2, 1, '2025-07-07 13:15:00', 'entree', 'autorisé'),
      (9, 1, '2025-07-07 14:00:00', 'entree', 'autorisé'),
      (10, 1, '2025-07-07 14:30:00', 'entree', 'autorisé'),
      (16, 5, '2025-07-07 15:00:00', 'entree', 'autorisé'),
      (17, 6, '2025-07-07 15:30:00', 'entree', 'autorisé'),
      (1, 2, '2025-07-07 17:00:00', 'sortie', 'autorisé'),
      (2, 2, '2025-07-07 17:15:00', 'sortie', 'autorisé'),
      (3, 2, '2025-07-07 17:30:00', 'sortie', 'autorisé')
    `);

    // Passages refusés
    await client.query(`
      INSERT INTO passage (id_badge, id_porte, date_acces, type_acces, resultat) VALUES 
      (NULL, 1, '2025-07-07 08:45:00', 'entree', 'refusé'),
      (NULL, 1, '2025-07-07 12:30:00', 'entree', 'refusé'),
      (NULL, 2, '2025-07-07 16:00:00', 'sortie', 'refusé')
    `);

    // Statistiques
    console.log('📊 Affichage des statistiques...');
    const stats = await client.query(`
      SELECT 'Types Personnel' as table_name, COUNT(*) as count FROM type_personnel
      UNION ALL SELECT 'Types Badge', COUNT(*) FROM type_badge
      UNION ALL SELECT 'Grades', COUNT(*) FROM grade
      UNION ALL SELECT 'Unités', COUNT(*) FROM unite
      UNION ALL SELECT 'Portes', COUNT(*) FROM porte
      UNION ALL SELECT 'Badges', COUNT(*) FROM badge
      UNION ALL SELECT 'Personnel', COUNT(*) FROM personnel
      UNION ALL SELECT 'Attributions', COUNT(*) FROM attribution_badge
      UNION ALL SELECT 'Passages', COUNT(*) FROM passage
    `);

    console.log('\n📈 STATISTIQUES DES DONNÉES INSÉRÉES:');
    stats.rows.forEach(row => {
      console.log(`   ${row.table_name}: ${row.count} enregistrements`);
    });

    // Afficher quelques attributions actives
    const attributions = await client.query(`
      SELECT p.nom, p.prenom, tp.nom_type as type_personnel, b.epc_code, tb.nom_type_badge
      FROM attribution_badge ab
      JOIN personnel p ON ab.id_personnel = p.id_personnel
      JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
      JOIN badge b ON ab.id_badge = b.id_badge
      JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
      WHERE ab.statut = 'actif'
      ORDER BY ab.date_attribution
      LIMIT 5
    `);

    console.log('\n👥 EXEMPLES D\'ATTRIBUTIONS ACTIVES:');
    attributions.rows.forEach(row => {
      console.log(`   ${row.nom} ${row.prenom} (${row.type_personnel}) - Badge: ${row.epc_code} (${row.nom_type_badge})`);
    });

    console.log('\n✅ Données de test insérées avec succès !');
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'insertion:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Exécuter le script
insertTestData();
