const db = require('../models/db');

// ========== RÉFÉRENTIELS ==========

// ========== TYPES DE BADGE ==========

// Lister les types de badge
const getTypesBadge = async (req, res) => {
  try {
    const result = await db.query('SELECT * FROM type_badge ORDER BY id_type_badge');
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération types badge :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Créer un type de badge
const createTypeBadge = async (req, res) => {
  const { nom_type_badge } = req.body;
  
  try {
    const result = await db.query(
      'INSERT INTO type_badge (nom_type_badge) VALUES ($1) RETURNING *',
      [nom_type_badge]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur création type badge :', error);
    res.status(500).json({ error: 'Erreur lors de la création' });
  }
};

// ========== TYPES DE PERSONNEL ==========

// Lister les types de personnel
const getTypesPersonnel = async (req, res) => {
  try {
    const result = await db.query('SELECT * FROM type_personnel ORDER BY id_type_personnel');
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération types personnel :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Créer un type de personnel
const createTypePersonnel = async (req, res) => {
  const { nom_type } = req.body;
  
  try {
    const result = await db.query(
      'INSERT INTO type_personnel (nom_type) VALUES ($1) RETURNING *',
      [nom_type]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur création type personnel :', error);
    res.status(500).json({ error: 'Erreur lors de la création' });
  }
};

// ========== UNITÉS ==========

// Lister les unités
const getUnites = async (req, res) => {
  try {
    const result = await db.query('SELECT * FROM unite ORDER BY nom_unite');
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération unités :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Créer une unité
const createUnite = async (req, res) => {
  const { nom_unite } = req.body;
  
  try {
    const result = await db.query(
      'INSERT INTO unite (nom_unite) VALUES ($1) RETURNING *',
      [nom_unite]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur création unité :', error);
    res.status(500).json({ error: 'Erreur lors de la création' });
  }
};

// Modifier une unité
const updateUnite = async (req, res) => {
  const { id } = req.params;
  const { nom_unite } = req.body;
  
  try {
    const result = await db.query(
      'UPDATE unite SET nom_unite = $1 WHERE id_unite = $2 RETURNING *',
      [nom_unite, id]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Unité non trouvée' });
    }
    
    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur modification unité :', error);
    res.status(500).json({ error: 'Erreur lors de la modification' });
  }
};

// Supprimer une unité
const deleteUnite = async (req, res) => {
  const { id } = req.params;
  
  try {
    // Vérifier qu'aucun personnel n'est associé à cette unité
    const personnelCheck = await db.query('SELECT COUNT(*) FROM personnel WHERE id_unite = $1', [id]);
    if (parseInt(personnelCheck.rows[0].count) > 0) {
      return res.status(400).json({ 
        error: 'Impossible de supprimer une unité associée à du personnel',
        personnel_associe: personnelCheck.rows[0].count
      });
    }
    
    const result = await db.query('DELETE FROM unite WHERE id_unite = $1 RETURNING *', [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Unité non trouvée' });
    }
    
    res.status(200).json({ message: 'Unité supprimée', unite: result.rows[0] });
  } catch (error) {
    console.error('❌ Erreur suppression unité :', error);
    res.status(500).json({ error: 'Erreur lors de la suppression' });
  }
};

// ========== GRADES ==========

// Lister les grades
const getGrades = async (req, res) => {
  try {
    const result = await db.query('SELECT * FROM grade ORDER BY nom_grade');
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération grades :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Créer un grade
const createGrade = async (req, res) => {
  const { nom_grade } = req.body;
  
  try {
    const result = await db.query(
      'INSERT INTO grade (nom_grade) VALUES ($1) RETURNING *',
      [nom_grade]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur création grade :', error);
    res.status(500).json({ error: 'Erreur lors de la création' });
  }
};

// Modifier un grade
const updateGrade = async (req, res) => {
  const { id } = req.params;
  const { nom_grade } = req.body;
  
  try {
    const result = await db.query(
      'UPDATE grade SET nom_grade = $1 WHERE id_grade = $2 RETURNING *',
      [nom_grade, id]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Grade non trouvé' });
    }
    
    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur modification grade :', error);
    res.status(500).json({ error: 'Erreur lors de la modification' });
  }
};

// Supprimer un grade
const deleteGrade = async (req, res) => {
  const { id } = req.params;
  
  try {
    // Vérifier qu'aucun personnel n'est associé à ce grade
    const personnelCheck = await db.query('SELECT COUNT(*) FROM personnel WHERE id_grade = $1', [id]);
    if (parseInt(personnelCheck.rows[0].count) > 0) {
      return res.status(400).json({ 
        error: 'Impossible de supprimer un grade associé à du personnel',
        personnel_associe: personnelCheck.rows[0].count
      });
    }
    
    const result = await db.query('DELETE FROM grade WHERE id_grade = $1 RETURNING *', [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Grade non trouvé' });
    }
    
    res.status(200).json({ message: 'Grade supprimé', grade: result.rows[0] });
  } catch (error) {
    console.error('❌ Erreur suppression grade :', error);
    res.status(500).json({ error: 'Erreur lors de la suppression' });
  }
};

// ========== PORTES ==========

// Lister les portes disponibles
const getPortes = async (req, res) => {
  try {
    const result = await db.query('SELECT * FROM porte ORDER BY libelle');
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération portes :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Créer une porte
const createPorte = async (req, res) => {
  const { libelle } = req.body;
  
  try {
    const result = await db.query(
      'INSERT INTO porte (libelle) VALUES ($1) RETURNING *',
      [libelle]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur création porte :', error);
    res.status(500).json({ error: 'Erreur lors de la création' });
  }
};

// Modifier une porte
const updatePorte = async (req, res) => {
  const { id } = req.params;
  const { libelle } = req.body;
  
  try {
    const result = await db.query(
      'UPDATE porte SET libelle = $1 WHERE id_porte = $2 RETURNING *',
      [libelle, id]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Porte non trouvée' });
    }
    
    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur modification porte :', error);
    res.status(500).json({ error: 'Erreur lors de la modification' });
  }
};

// Supprimer une porte
const deletePorte = async (req, res) => {
  const { id } = req.params;
  
  try {
    // Vérifier qu'aucun passage n'est associé à cette porte
    const passageCheck = await db.query('SELECT COUNT(*) FROM passage WHERE id_porte = $1', [id]);
    if (parseInt(passageCheck.rows[0].count) > 0) {
      return res.status(400).json({ 
        error: 'Impossible de supprimer une porte avec des passages enregistrés',
        passages_associes: passageCheck.rows[0].count
      });
    }
    
    const result = await db.query('DELETE FROM porte WHERE id_porte = $1 RETURNING *', [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Porte non trouvée' });
    }
    
    res.status(200).json({ message: 'Porte supprimée', porte: result.rows[0] });
  } catch (error) {
    console.error('❌ Erreur suppression porte :', error);
    res.status(500).json({ error: 'Erreur lors de la suppression' });
  }
};

module.exports = {
  // Types de badge
  getTypesBadge,
  createTypeBadge,
  
  // Types de personnel
  getTypesPersonnel,
  createTypePersonnel,
  
  // Unités
  getUnites,
  createUnite,
  updateUnite,
  deleteUnite,
  
  // Grades
  getGrades,
  createGrade,
  updateGrade,
  deleteGrade,
  
  // Portes
  getPortes,
  createPorte,
  updatePorte,
  deletePorte,
};
