import { useState, useEffect } from 'react';
import { referentielAPI } from '../services/api';

function ReferentielManagement() {
  const [activeTab, setActiveTab] = useState('types_personnel');
  const [data, setData] = useState({
    types_personnel: [],
    types_badge: [],
    grades: [],
    unites: [],
    portes: []
  });
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newItem, setNewItem] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [typesPersonnelRes, typesBadgeRes, gradesRes, unitesRes, portesRes] = await Promise.all([
        referentielAPI.getTypesPersonnel(),
        referentielAPI.getTypesBadge(),
        referentielAPI.getGrades(),
        referentielAPI.getUnites(),
        referentielAPI.getPortes()
      ]);
      
      setData({
        types_personnel: typesPersonnelRes.data,
        types_badge: typesBadgeRes.data,
        grades: gradesRes.data,
        unites: unitesRes.data,
        portes: portesRes.data
      });
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async (e) => {
    e.preventDefault();
    try {
      const payload = getPayload(activeTab, newItem);
      
      switch (activeTab) {
        case 'types_personnel':
          await referentielAPI.createTypePersonnel(payload);
          break;
        case 'types_badge':
          await referentielAPI.createTypeBadge(payload);
          break;
        case 'grades':
          await referentielAPI.createGrade(payload);
          break;
        case 'unites':
          await referentielAPI.createUnite(payload);
          break;
        case 'portes':
          await referentielAPI.createPorte(payload);
          break;
      }
      
      setNewItem('');
      setShowCreateForm(false);
      loadData();
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      alert('Erreur lors de la création');
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
      try {
        switch (activeTab) {
          case 'grades':
            await referentielAPI.deleteGrade(id);
            break;
          case 'unites':
            await referentielAPI.deleteUnite(id);
            break;
          case 'portes':
            await referentielAPI.deletePorte(id);
            break;
          default:
            alert('Suppression non disponible pour ce type');
            return;
        }
        loadData();
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        alert('Erreur lors de la suppression');
      }
    }
  };

  const getPayload = (type, value) => {
    switch (type) {
      case 'types_personnel':
        return { nom_type: value };
      case 'types_badge':
        return { nom_type_badge: value };
      case 'grades':
        return { nom_grade: value };
      case 'unites':
        return { nom_unite: value };
      case 'portes':
        return { libelle: value };
      default:
        return {};
    }
  };

  const getFieldName = (type) => {
    switch (type) {
      case 'types_personnel':
        return 'nom_type';
      case 'types_badge':
        return 'nom_type_badge';
      case 'grades':
        return 'nom_grade';
      case 'unites':
        return 'nom_unite';
      case 'portes':
        return 'libelle';
      default:
        return '';
    }
  };

  const getIdField = (type) => {
    switch (type) {
      case 'types_personnel':
        return 'id_type_personnel';
      case 'types_badge':
        return 'id_type_badge';
      case 'grades':
        return 'id_grade';
      case 'unites':
        return 'id_unite';
      case 'portes':
        return 'id_porte';
      default:
        return '';
    }
  };

  const tabs = [
    { id: 'types_personnel', label: 'Types Personnel', icon: '👥' },
    { id: 'types_badge', label: 'Types Badge', icon: '🪪' },
    { id: 'grades', label: 'Grades', icon: '🎖️' },
    { id: 'unites', label: 'Unités', icon: '🏢' },
    { id: 'portes', label: 'Portes', icon: '🚪' }
  ];

  const canDelete = (type) => {
    return ['grades', 'unites', 'portes'].includes(type);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Chargement...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Gestion des Référentiels</h2>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          + Ajouter
        </button>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {tabs.map((tab) => (
          <div key={tab.id} className="bg-white p-4 rounded-lg shadow">
            <div className="text-xl font-bold text-blue-600">{data[tab.id].length}</div>
            <div className="text-gray-600 text-sm">{tab.label}</div>
          </div>
        ))}
      </div>

      {/* Onglets */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Contenu de l'onglet */}
        <div className="p-6">
          <div className="overflow-hidden">
            <table className="min-w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nom</th>
                  {canDelete(activeTab) && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                  )}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {data[activeTab].map((item) => (
                  <tr key={item[getIdField(activeTab)]}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item[getIdField(activeTab)]}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item[getFieldName(activeTab)]}
                    </td>
                    {canDelete(activeTab) && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleDelete(item[getIdField(activeTab)])}
                          className="text-red-600 hover:text-red-900"
                        >
                          Supprimer
                        </button>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Modal de création */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg shadow-lg w-96">
            <h3 className="text-lg font-medium mb-4">
              Ajouter {tabs.find(t => t.id === activeTab)?.label}
            </h3>
            <form onSubmit={handleCreate}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nom
                </label>
                <input
                  type="text"
                  value={newItem}
                  onChange={(e) => setNewItem(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder={`Nom du ${tabs.find(t => t.id === activeTab)?.label.toLowerCase()}`}
                  required
                />
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Ajouter
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default ReferentielManagement;
