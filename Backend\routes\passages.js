const express = require('express');
const router = express.Router();
const { 
  createPassage,
  getAllPassages,
  getPassagesRecents,
  getPassagesPersonnel,
  getPassagesBadge,
  getStatistiquesPassages
} = require('../controllers/passageController');

// ========== ROUTES PASSAGES / CONTRÔLE D'ACCÈS ==========

// POST /api/passages - Enregistrer un passage (C72 envoie EPC, porte, date, type accès)
router.post('/', createPassage);

// GET /api/passages/recent - Lister les passages récents (temps réel : ex. les 20 derniers)
router.get('/recent', getPassagesRecents);

// GET /api/passages/statistiques - Statistiques des passages (optionnel)
router.get('/statistiques', getStatistiquesPassages);

// GET /api/passages/personnel/:id_personnel - Historique des passages d'un personnel
router.get('/personnel/:id_personnel', getPassagesPersonnel);

// GET /api/passages/badge/:id_badge - Historique d'un badge
router.get('/badge/:id_badge', getPassagesBadge);

// GET /api/passages - Lister tous les passages
router.get('/', getAllPassages);

module.exports = router;
