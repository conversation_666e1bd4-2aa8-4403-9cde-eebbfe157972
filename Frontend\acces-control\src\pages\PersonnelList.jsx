import { useState, useEffect } from 'react';
import { personnelAPI, referentielAPI, badgeAPI } from '../services/api';

function PersonnelList() {
  const [personnelsInternes, setPersonnelsInternes] = useState([]);
  const [personnelsExternes, setPersonnelsExternes] = useState([]);
  const [grades, setGrades] = useState([]);
  const [unites, setUnites] = useState([]);
  const [typesBadge, setTypesBadge] = useState([]);
  const [badgesDisponibles, setBadgesDisponibles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('internes');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newPersonnel, setNewPersonnel] = useState({
    nom: '',
    prenom: '',
    id_grade: '',
    id_unite: '',
    id_type_personnel: '',
    id_badge: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [internesRes, externesRes, gradesRes, unitesRes, typesBadgeRes, badgesRes] = await Promise.all([
        personnelAPI.getPersonnelsInternes(),
        personnelAPI.getPersonnelsExternes(),
        referentielAPI.getGrades(),
        referentielAPI.getUnites(),
        referentielAPI.getTypesBadge(),
        badgeAPI.getBadgesDisponibles()
      ]);

      setPersonnelsInternes(internesRes.data);
      setPersonnelsExternes(externesRes.data);
      setGrades(gradesRes.data);
      setUnites(unitesRes.data);
      setTypesBadge(typesBadgeRes.data);
      setBadgesDisponibles(badgesRes.data);
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePersonnel = async (e) => {
    e.preventDefault();
    try {
      if (activeTab === 'internes') {
        // Personnel interne - badge automatique
        await personnelAPI.createPersonnelInterne({
          nom: newPersonnel.nom,
          prenom: newPersonnel.prenom,
          id_grade: newPersonnel.id_grade,
          id_unite: newPersonnel.id_unite
        });
      } else {
        // Personnel externe - badge manuel
        await personnelAPI.createPersonnelExterne({
          nom: newPersonnel.nom,
          prenom: newPersonnel.prenom,
          id_type_personnel: newPersonnel.id_type_personnel,
          id_grade: newPersonnel.id_grade,
          id_unite: newPersonnel.id_unite,
          id_badge: newPersonnel.id_badge
        });
      }

      setNewPersonnel({
        nom: '', prenom: '', id_grade: '', id_unite: '',
        id_type_personnel: '', id_badge: ''
      });
      setShowCreateForm(false);
      loadData();
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      alert('Erreur lors de la création du personnel');
    }
  };

  const handleDeletePersonnel = async (id, type) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce personnel ?')) {
      try {
        if (type === 'interne') {
          await personnelAPI.deletePersonnelInterne(id, 'logique');
        } else {
          await personnelAPI.deletePersonnelExterne(id);
        }
        loadData();
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        alert('Erreur lors de la suppression');
      }
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Chargement...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Gestion du Personnel</h2>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          + Nouveau Personnel
        </button>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-blue-600">{personnelsInternes.length}</div>
          <div className="text-gray-600">Personnel Interne</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">{personnelsExternes.length}</div>
          <div className="text-gray-600">Personnel Externe</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-purple-600">
            {personnelsInternes.length + personnelsExternes.length}
          </div>
          <div className="text-gray-600">Total Personnel</div>
        </div>
      </div>

      {/* Onglets */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('internes')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'internes'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              👥 Personnel Interne ({personnelsInternes.length})
            </button>
            <button
              onClick={() => setActiveTab('externes')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'externes'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              🌐 Personnel Externe ({personnelsExternes.length})
            </button>
          </nav>
        </div>

        {/* Liste du personnel */}
        <div className="p-6">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nom</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Grade</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Unité</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Badge</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Statut Badge</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {(activeTab === 'internes' ? personnelsInternes : personnelsExternes).map((personnel) => (
                <tr key={personnel.id_personnel}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {personnel.nom} {personnel.prenom}
                    </div>
                    <div className="text-sm text-gray-500">
                      {personnel.nom_type}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {personnel.nom_grade}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {personnel.nom_unite}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                    {personnel.epc_code || 'Aucun badge'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      personnel.statut_badge === 'actif'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {personnel.statut_badge === 'actif' ? 'Badge Actif' : 'Pas de badge'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => handleDeletePersonnel(personnel.id_personnel, activeTab === 'internes' ? 'interne' : 'externe')}
                      className="text-red-600 hover:text-red-900"
                    >
                      Supprimer
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal de création */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg shadow-lg w-96">
            <h3 className="text-lg font-medium mb-4">
              Créer un Personnel {activeTab === 'internes' ? 'Interne' : 'Externe'}
            </h3>
            <form onSubmit={handleCreatePersonnel}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Nom</label>
                <input
                  type="text"
                  value={newPersonnel.nom}
                  onChange={(e) => setNewPersonnel({...newPersonnel, nom: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Prénom</label>
                <input
                  type="text"
                  value={newPersonnel.prenom}
                  onChange={(e) => setNewPersonnel({...newPersonnel, prenom: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                />
              </div>

              {activeTab === 'externes' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                  <select
                    value={newPersonnel.id_type_personnel}
                    onChange={(e) => setNewPersonnel({...newPersonnel, id_type_personnel: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  >
                    <option value="">Sélectionner un type</option>
                    <option value="2">Externe</option>
                    <option value="3">Militaire</option>
                    <option value="4">Civil</option>
                  </select>
                </div>
              )}

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Grade</label>
                <select
                  value={newPersonnel.id_grade}
                  onChange={(e) => setNewPersonnel({...newPersonnel, id_grade: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                >
                  <option value="">Sélectionner un grade</option>
                  {grades.map((grade) => (
                    <option key={grade.id_grade} value={grade.id_grade}>
                      {grade.nom_grade}
                    </option>
                  ))}
                </select>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Unité</label>
                <select
                  value={newPersonnel.id_unite}
                  onChange={(e) => setNewPersonnel({...newPersonnel, id_unite: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                >
                  <option value="">Sélectionner une unité</option>
                  {unites.map((unite) => (
                    <option key={unite.id_unite} value={unite.id_unite}>
                      {unite.nom_unite}
                    </option>
                  ))}
                </select>
              </div>

              {activeTab === 'externes' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Badge (optionnel)
                  </label>
                  <select
                    value={newPersonnel.id_badge}
                    onChange={(e) => setNewPersonnel({...newPersonnel, id_badge: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="">Aucun badge pour le moment</option>
                    {badgesDisponibles.map((badge) => (
                      <option key={badge.id_badge} value={badge.id_badge}>
                        {badge.epc_code} ({badge.nom_type_badge})
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {activeTab === 'internes' && (
                <div className="mb-4 p-3 bg-blue-50 rounded-md">
                  <p className="text-sm text-blue-700">
                    ℹ️ Un badge sera automatiquement attribué lors de la création
                  </p>
                </div>
              )}

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Créer Personnel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default PersonnelList;
