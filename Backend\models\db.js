const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

// Test de connexion
pool.connect((err, client, release) => {
  if (err) {
    return console.error('❌ Échec de connexion à PostgreSQL :', err.stack);
  }
  console.log('✅ Connexion à PostgreSQL établie avec succès ✔️');
  release();
});

module.exports = pool;
