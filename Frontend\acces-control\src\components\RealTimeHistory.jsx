export function RealTimeHistory({ personnels }) {
  return (
    <div className="bg-white rounded-lg shadow p-4 mb-6">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-lg font-semibold">Historique en temps réel</h2>
        <button className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">Ajouter</button>
      </div>
      <table className="w-full text-sm">
        <thead>
          <tr className="text-left text-gray-500 border-b">
            <th className="py-2">Nom</th>
            <th className="py-2">Prénom</th>
          </tr>
        </thead>
        <tbody>
          {personnels.slice(0, 5).map(p => (
            <tr key={p.id_personnel} className="border-b">
              <td className="py-2">{p.nom}</td>
              <td className="py-2">{p.prenom}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}