# Exemples d'Utilisation des APIs

## 🔧 Test de Connectivité

```bash
# Test ping
curl -X GET http://localhost:3000/api/ping
```

**Réponse :**
```json
{"message":"API opérationnelle 🎯"}
```

---

## 👤 Gestion des Personnels

### Créer un Personnel Interne (avec badge automatique)

```bash
curl -X POST http://localhost:3000/api/personnel/internes \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "<PERSON>pont",
    "prenom": "<PERSON>",
    "id_grade": 1,
    "id_unite": 1
  }'
```

**Réponse :**
```json
{
  "personnel": {
    "id_personnel": 1,
    "nom": "<PERSON><PERSON>",
    "prenom": "<PERSON>",
    "id_type_personnel": 1,
    "id_grade": 1,
    "id_unite": 1
  },
  "badge_attribue": {
    "id_badge": 5,
    "epc_code": "E200001617501234",
    "id_type_badge": 1
  },
  "message": "Personnel interne créé avec badge automatiquement attribué"
}
```

### Créer un Personnel Externe (avec badge manuel)

```bash
curl -X POST http://localhost:3000/api/personnel/externes \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Martin",
    "prenom": "Pierre",
    "id_type_personnel": 2,
    "id_grade": 2,
    "id_unite": 2,
    "id_badge": 10
  }'
```

---

## 🪪 Gestion des Badges

### Créer un Badge

```bash
curl -X POST http://localhost:3000/api/badges \
  -H "Content-Type: application/json" \
  -d '{
    "epc_code": "E200001617501234",
    "id_type_badge": 1
  }'
```

### Lister les Badges Disponibles

```bash
curl -X GET http://localhost:3000/api/badges/disponibles
```

### Lister les Badges Disponibles par Type

```bash
curl -X GET "http://localhost:3000/api/badges/disponibles?type_badge=1"
```

---

## 🔄 Attribution des Badges

### Attribution Automatique

```bash
curl -X POST http://localhost:3000/api/attributions \
  -H "Content-Type: application/json" \
  -d '{
    "id_personnel": 1,
    "mode": "automatique"
  }'
```

### Attribution Manuelle

```bash
curl -X POST http://localhost:3000/api/attributions \
  -H "Content-Type: application/json" \
  -d '{
    "id_personnel": 1,
    "id_badge": 5,
    "mode": "manuel"
  }'
```

### Clôturer une Attribution

```bash
curl -X PUT http://localhost:3000/api/attributions/1/fin \
  -H "Content-Type: application/json" \
  -d '{
    "motif": "Fin de mission"
  }'
```

---

## 🚪 Enregistrement des Passages

### Enregistrer un Passage (depuis C72)

```bash
curl -X POST http://localhost:3000/api/passages \
  -H "Content-Type: application/json" \
  -d '{
    "epc_code": "E200001617501234",
    "id_porte": 1,
    "type_acces": "entree",
    "date_acces": "2025-07-07T21:30:00Z"
  }'
```

**Réponse (Accès Autorisé) :**
```json
{
  "passage": {
    "id_journal": 1,
    "id_badge": 5,
    "id_porte": 1,
    "date_acces": "2025-07-07T21:30:00.000Z",
    "type_acces": "entree",
    "resultat": "autorisé"
  },
  "resultat": "autorisé",
  "motif": "Accès autorisé",
  "badge": {
    "id_badge": 5,
    "epc_code": "E200001617501234"
  },
  "personnel": {
    "nom": "Dupont",
    "prenom": "Jean"
  }
}
```

**Réponse (Accès Refusé) :**
```json
{
  "passage": {
    "id_journal": 2,
    "id_badge": null,
    "id_porte": 1,
    "date_acces": "2025-07-07T21:35:00.000Z",
    "type_acces": "entree",
    "resultat": "refusé"
  },
  "resultat": "refusé",
  "motif": "Badge non reconnu",
  "epc_code": "E200001617509999"
}
```

### Consulter les Passages Récents

```bash
curl -X GET "http://localhost:3000/api/passages/recent?limit=10"
```

---

## 🧾 Gestion des Référentiels

### Créer des Types de Base

```bash
# Types de personnel
curl -X POST http://localhost:3000/api/types_personnel \
  -H "Content-Type: application/json" \
  -d '{"nom_type": "Interne"}'

curl -X POST http://localhost:3000/api/types_personnel \
  -H "Content-Type: application/json" \
  -d '{"nom_type": "Externe"}'

# Types de badge
curl -X POST http://localhost:3000/api/types_badge \
  -H "Content-Type: application/json" \
  -d '{"nom_type_badge": "Badge Interne"}'

# Grades
curl -X POST http://localhost:3000/api/grades \
  -H "Content-Type: application/json" \
  -d '{"nom_grade": "Capitaine"}'

# Unités
curl -X POST http://localhost:3000/api/unites \
  -H "Content-Type: application/json" \
  -d '{"nom_unite": "1er Régiment"}'

# Portes
curl -X POST http://localhost:3000/api/portes \
  -H "Content-Type: application/json" \
  -d '{"libelle": "Entrée Principale"}'
```

---

## 📊 Consultation des Données

### Lister les Attributions Actives

```bash
curl -X GET http://localhost:3000/api/attributions/actives
```

### Historique des Passages d'un Personnel

```bash
curl -X GET http://localhost:3000/api/passages/personnel/1
```

### Historique d'un Badge

```bash
curl -X GET http://localhost:3000/api/passages/badge/5
```

### Statistiques des Passages

```bash
curl -X GET "http://localhost:3000/api/passages/statistiques?date_debut=2025-07-01&date_fin=2025-07-31"
```

---

## 🔍 Filtres et Pagination

### Passages avec Filtres

```bash
# Passages autorisés seulement
curl -X GET "http://localhost:3000/api/passages?resultat=autorisé&limit=50&page=1"

# Passages entre deux dates
curl -X GET "http://localhost:3000/api/passages?date_debut=2025-07-01&date_fin=2025-07-31"
```

### Attributions avec Pagination

```bash
curl -X GET "http://localhost:3000/api/attributions?statut=actif&page=1&limit=20"
```

---

## ⚠️ Gestion des Erreurs

### Exemples de Réponses d'Erreur

**Badge non trouvé :**
```json
{
  "error": "Badge non trouvé"
}
```

**Personnel déjà avec badge actif :**
```json
{
  "error": "Ce personnel a déjà un badge actif"
}
```

**Aucun badge disponible :**
```json
{
  "error": "Aucun badge de type 1 disponible"
}
```

---

## 🚀 Workflow Complet

### Scénario : Nouveau Personnel Interne

1. **Créer le personnel** → Badge attribué automatiquement
2. **Vérifier l'attribution** → Consulter les attributions actives
3. **Premier passage** → Enregistrement automatique
4. **Suivi** → Consulter l'historique des passages

### Scénario : Personnel Externe Temporaire

1. **Créer le personnel externe** avec badge spécifique
2. **Période d'activité** → Passages enregistrés
3. **Fin de mission** → Clôturer l'attribution
4. **Badge libéré** → Disponible pour réattribution
