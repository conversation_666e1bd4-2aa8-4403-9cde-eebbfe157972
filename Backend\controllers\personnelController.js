const db = require('../models/db');

// Récupérer tous les personnels
const getAllPersonnels = async (req, res) => {
  try {
    const result = await db.query('SELECT * FROM personnel ORDER BY id_personnel');
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération personnels :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

const createPersonnel = async (req, res) => {
  const { nom, prenom, id_type_personnel, id_grade, id_unite } = req.body;
  try {
    const result = await db.query(
      'INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [nom, prenom, id_type_personnel, id_grade, id_unite]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur lors de la création du personnel :', error);
    res.status(500).json({ error: 'Erreur lors de la création du personnel' });
  }
};

module.exports = {
  getAllPersonnels,
  createPersonnel,
};
