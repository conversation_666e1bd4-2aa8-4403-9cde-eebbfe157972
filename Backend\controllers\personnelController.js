const db = require('../models/db');

// ========== PERSONNEL INTERNE ==========

// Créer un personnel interne avec badge automatiquement attribué
const createPersonnelInterne = async (req, res) => {
  const { nom, prenom, id_grade, id_unite } = req.body;
  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // 1. Créer le personnel interne (type_personnel = 1 pour interne)
    const personnelResult = await client.query(
      'INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [nom, prenom, 1, id_grade, id_unite]
    );
    const personnel = personnelResult.rows[0];

    // 2. Trouver un badge libre de type interne
    const badgeResult = await client.query(`
      SELECT b.* FROM badge b
      LEFT JOIN attribution_badge ab ON b.id_badge = ab.id_badge AND ab.statut = 'actif'
      WHERE b.id_type_badge = 1 AND ab.id_badge IS NULL
      LIMIT 1
    `);

    if (badgeResult.rows.length === 0) {
      throw new Error('Aucun badge interne disponible');
    }

    const badge = badgeResult.rows[0];

    // 3. Attribuer automatiquement le badge
    await client.query(
      'INSERT INTO attribution_badge (id_personnel, id_badge, date_attribution, statut) VALUES ($1, $2, CURRENT_DATE, $3)',
      [personnel.id_personnel, badge.id_badge, 'actif']
    );

    await client.query('COMMIT');

    res.status(201).json({
      personnel,
      badge_attribue: badge,
      message: 'Personnel interne créé avec badge automatiquement attribué'
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Erreur création personnel interne :', error);
    res.status(500).json({ error: error.message || 'Erreur lors de la création du personnel interne' });
  } finally {
    client.release();
  }
};

// Lister tous les personnels internes
const getPersonnelsInternes = async (req, res) => {
  try {
    const result = await db.query(`
      SELECT p.*, tp.nom_type, g.nom_grade, u.nom_unite,
             b.epc_code, b.id_badge, ab.date_attribution, ab.statut as statut_badge
      FROM personnel p
      LEFT JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
      LEFT JOIN grade g ON p.id_grade = g.id_grade
      LEFT JOIN unite u ON p.id_unite = u.id_unite
      LEFT JOIN attribution_badge ab ON p.id_personnel = ab.id_personnel AND ab.statut = 'actif'
      LEFT JOIN badge b ON ab.id_badge = b.id_badge
      WHERE p.id_type_personnel = 1
      ORDER BY p.id_personnel
    `);
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération personnels internes :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Détails d'un personnel interne
const getPersonnelInterneById = async (req, res) => {
  const { id } = req.params;
  try {
    const result = await db.query(`
      SELECT p.*, tp.nom_type, g.nom_grade, u.nom_unite,
             b.epc_code, b.id_badge, ab.date_attribution, ab.statut as statut_badge
      FROM personnel p
      LEFT JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
      LEFT JOIN grade g ON p.id_grade = g.id_grade
      LEFT JOIN unite u ON p.id_unite = u.id_unite
      LEFT JOIN attribution_badge ab ON p.id_personnel = ab.id_personnel AND ab.statut = 'actif'
      LEFT JOIN badge b ON ab.id_badge = b.id_badge
      WHERE p.id_personnel = $1 AND p.id_type_personnel = 1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Personnel interne non trouvé' });
    }

    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur récupération personnel interne :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Modifier un personnel interne
const updatePersonnelInterne = async (req, res) => {
  const { id } = req.params;
  const { nom, prenom, id_grade, id_unite } = req.body;

  try {
    const result = await db.query(
      'UPDATE personnel SET nom = $1, prenom = $2, id_grade = $3, id_unite = $4 WHERE id_personnel = $5 AND id_type_personnel = 1 RETURNING *',
      [nom, prenom, id_grade, id_unite, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Personnel interne non trouvé' });
    }

    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur modification personnel interne :', error);
    res.status(500).json({ error: 'Erreur lors de la modification' });
  }
};

// Supprimer un personnel interne (logique)
const deletePersonnelInterne = async (req, res) => {
  const { id } = req.params;
  const { type = 'logique' } = req.query; // logique par défaut, physique si spécifié

  const client = await db.connect();

  try {
    await client.query('BEGIN');

    if (type === 'physique') {
      // Suppression physique : désactiver d'abord les attributions
      await client.query(
        "UPDATE attribution_badge SET statut = 'désactivé', date_fin = CURRENT_DATE WHERE id_personnel = $1 AND statut = 'actif'",
        [id]
      );

      // Puis supprimer le personnel
      const result = await client.query(
        'DELETE FROM personnel WHERE id_personnel = $1 AND id_type_personnel = 1 RETURNING *',
        [id]
      );

      if (result.rows.length === 0) {
        throw new Error('Personnel interne non trouvé');
      }

      await client.query('COMMIT');
      res.status(200).json({ message: 'Personnel interne supprimé définitivement', personnel: result.rows[0] });

    } else {
      // Suppression logique : désactiver les attributions seulement
      const result = await client.query(
        "UPDATE attribution_badge SET statut = 'désactivé', date_fin = CURRENT_DATE WHERE id_personnel = $1 AND statut = 'actif' RETURNING *",
        [id]
      );

      await client.query('COMMIT');
      res.status(200).json({ message: 'Personnel interne désactivé (suppression logique)', attributions_desactivees: result.rows });
    }

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Erreur suppression personnel interne :', error);
    res.status(500).json({ error: error.message || 'Erreur lors de la suppression' });
  } finally {
    client.release();
  }
};

// ========== PERSONNEL EXTERNE ==========

// Créer un personnel externe (badge sélectionné manuellement)
const createPersonnelExterne = async (req, res) => {
  const { nom, prenom, id_type_personnel, id_grade, id_unite, id_badge } = req.body;
  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Vérifier que le type de personnel est externe (2, 3, ou 4)
    if (![2, 3, 4].includes(id_type_personnel)) {
      throw new Error('Type de personnel invalide pour un personnel externe');
    }

    // 1. Créer le personnel externe
    const personnelResult = await client.query(
      'INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [nom, prenom, id_type_personnel, id_grade, id_unite]
    );
    const personnel = personnelResult.rows[0];

    // 2. Si un badge est spécifié, vérifier qu'il est disponible et l'attribuer
    if (id_badge) {
      const badgeCheck = await client.query(`
        SELECT b.* FROM badge b
        LEFT JOIN attribution_badge ab ON b.id_badge = ab.id_badge AND ab.statut = 'actif'
        WHERE b.id_badge = $1 AND ab.id_badge IS NULL
      `, [id_badge]);

      if (badgeCheck.rows.length === 0) {
        throw new Error('Badge non disponible ou inexistant');
      }

      // Attribuer le badge
      await client.query(
        'INSERT INTO attribution_badge (id_personnel, id_badge, date_attribution, statut) VALUES ($1, $2, CURRENT_DATE, $3)',
        [personnel.id_personnel, id_badge, 'actif']
      );
    }

    await client.query('COMMIT');

    res.status(201).json({
      personnel,
      badge_attribue: id_badge ? { id_badge } : null,
      message: 'Personnel externe créé' + (id_badge ? ' avec badge attribué' : '')
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Erreur création personnel externe :', error);
    res.status(500).json({ error: error.message || 'Erreur lors de la création du personnel externe' });
  } finally {
    client.release();
  }
};

// Lister tous les personnels externes
const getPersonnelsExternes = async (req, res) => {
  try {
    const result = await db.query(`
      SELECT p.*, tp.nom_type, g.nom_grade, u.nom_unite,
             b.epc_code, b.id_badge, ab.date_attribution, ab.statut as statut_badge
      FROM personnel p
      LEFT JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
      LEFT JOIN grade g ON p.id_grade = g.id_grade
      LEFT JOIN unite u ON p.id_unite = u.id_unite
      LEFT JOIN attribution_badge ab ON p.id_personnel = ab.id_personnel AND ab.statut = 'actif'
      LEFT JOIN badge b ON ab.id_badge = b.id_badge
      WHERE p.id_type_personnel IN (2, 3, 4)
      ORDER BY p.id_personnel
    `);
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération personnels externes :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Détails d'un personnel externe
const getPersonnelExterneById = async (req, res) => {
  const { id } = req.params;
  try {
    const result = await db.query(`
      SELECT p.*, tp.nom_type, g.nom_grade, u.nom_unite,
             b.epc_code, b.id_badge, ab.date_attribution, ab.statut as statut_badge
      FROM personnel p
      LEFT JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
      LEFT JOIN grade g ON p.id_grade = g.id_grade
      LEFT JOIN unite u ON p.id_unite = u.id_unite
      LEFT JOIN attribution_badge ab ON p.id_personnel = ab.id_personnel AND ab.statut = 'actif'
      LEFT JOIN badge b ON ab.id_badge = b.id_badge
      WHERE p.id_personnel = $1 AND p.id_type_personnel IN (2, 3, 4)
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Personnel externe non trouvé' });
    }

    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur récupération personnel externe :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Modifier un personnel externe
const updatePersonnelExterne = async (req, res) => {
  const { id } = req.params;
  const { nom, prenom, id_type_personnel, id_grade, id_unite } = req.body;

  try {
    // Vérifier que le type de personnel est externe
    if (id_type_personnel && ![2, 3, 4].includes(id_type_personnel)) {
      return res.status(400).json({ error: 'Type de personnel invalide pour un personnel externe' });
    }

    const result = await db.query(
      'UPDATE personnel SET nom = $1, prenom = $2, id_type_personnel = $3, id_grade = $4, id_unite = $5 WHERE id_personnel = $6 AND id_type_personnel IN (2, 3, 4) RETURNING *',
      [nom, prenom, id_type_personnel, id_grade, id_unite, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Personnel externe non trouvé' });
    }

    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur modification personnel externe :', error);
    res.status(500).json({ error: 'Erreur lors de la modification' });
  }
};

// Supprimer un personnel externe
const deletePersonnelExterne = async (req, res) => {
  const { id } = req.params;
  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Désactiver les attributions de badges
    await client.query(
      "UPDATE attribution_badge SET statut = 'désactivé', date_fin = CURRENT_DATE WHERE id_personnel = $1 AND statut = 'actif'",
      [id]
    );

    // Supprimer le personnel externe
    const result = await client.query(
      'DELETE FROM personnel WHERE id_personnel = $1 AND id_type_personnel IN (2, 3, 4) RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      throw new Error('Personnel externe non trouvé');
    }

    await client.query('COMMIT');
    res.status(200).json({ message: 'Personnel externe supprimé', personnel: result.rows[0] });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Erreur suppression personnel externe :', error);
    res.status(500).json({ error: error.message || 'Erreur lors de la suppression' });
  } finally {
    client.release();
  }
};

// ========== FONCTIONS DE COMPATIBILITÉ ==========

// Récupérer tous les personnels (fonction existante maintenue pour compatibilité)
const getAllPersonnels = async (req, res) => {
  try {
    const result = await db.query(`
      SELECT p.*, tp.nom_type, g.nom_grade, u.nom_unite,
             b.epc_code, b.id_badge, ab.date_attribution, ab.statut as statut_badge
      FROM personnel p
      LEFT JOIN type_personnel tp ON p.id_type_personnel = tp.id_type_personnel
      LEFT JOIN grade g ON p.id_grade = g.id_grade
      LEFT JOIN unite u ON p.id_unite = u.id_unite
      LEFT JOIN attribution_badge ab ON p.id_personnel = ab.id_personnel AND ab.statut = 'actif'
      LEFT JOIN badge b ON ab.id_badge = b.id_badge
      ORDER BY p.id_personnel
    `);
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('❌ Erreur récupération personnels :', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Créer un personnel (fonction existante maintenue pour compatibilité)
const createPersonnel = async (req, res) => {
  const { nom, prenom, id_type_personnel, id_grade, id_unite } = req.body;
  try {
    const result = await db.query(
      'INSERT INTO personnel (nom, prenom, id_type_personnel, id_grade, id_unite) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [nom, prenom, id_type_personnel, id_grade, id_unite]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ Erreur lors de la création du personnel :', error);
    res.status(500).json({ error: 'Erreur lors de la création du personnel' });
  }
};

module.exports = {
  // Fonctions de compatibilité (existantes)
  getAllPersonnels,
  createPersonnel,

  // Personnel interne
  createPersonnelInterne,
  getPersonnelsInternes,
  getPersonnelInterneById,
  updatePersonnelInterne,
  deletePersonnelInterne,

  // Personnel externe
  createPersonnelExterne,
  getPersonnelsExternes,
  getPersonnelExterneById,
  updatePersonnelExterne,
  deletePersonnelExterne,
};
