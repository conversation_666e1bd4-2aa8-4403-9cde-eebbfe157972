-- ========== STRUCTURE MÉTIER - SYSTÈME DE CONTRÔLE D'ACCÈS ==========

-- Supprimer les tables existantes dans l'ordre des dépendances
DROP TABLE IF EXISTS passage CASCADE;
DROP TABLE IF EXISTS attribution_badge CASCADE;
DROP TABLE IF EXISTS civil_info CASCADE;
DROP TABLE IF EXISTS militaire_externe_info CASCADE;
DROP TABLE IF EXISTS personnel CASCADE;
DROP TABLE IF EXISTS badge CASCADE;
DROP TABLE IF EXISTS type_badge CASCADE;
DROP TABLE IF EXISTS grade CASCADE;
DROP TABLE IF EXISTS unite CASCADE;
DROP TABLE IF EXISTS porte CASCADE;

-- ========== RÉFÉRENTIELS ==========

-- Grades militaires
CREATE TABLE grade (
    id_grade SERIAL PRIMARY KEY,
    nom_grade VARCHAR(50) NOT NULL UNIQUE
);

-- Unités militaires
CREATE TABLE unite (
    id_unite SERIAL PRIMARY KEY,
    nom_unite VARCHAR(100) NOT NULL UNIQUE
);

-- Types de badges selon la logique métier
CREATE TABLE type_badge (
    id_type_badge SERIAL PRIMARY KEY,
    nom_type_badge VARCHAR(50) NOT NULL UNIQUE,
    permanent BOOLEAN NOT NULL DEFAULT FALSE,
    reutilisable BOOLEAN NOT NULL DEFAULT TRUE,
    description TEXT
);

-- Portes d'accès
CREATE TABLE porte (
    id_porte SERIAL PRIMARY KEY,
    libelle VARCHAR(50) NOT NULL UNIQUE,
    localisation VARCHAR(100)
);

-- ========== BADGES ==========

CREATE TABLE badge (
    id_badge SERIAL PRIMARY KEY,
    epc_code VARCHAR(32) UNIQUE NOT NULL,
    id_type_badge INT REFERENCES type_badge(id_type_badge) NOT NULL,
    actif BOOLEAN NOT NULL DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    commentaire TEXT
);

-- ========== PERSONNEL ==========

-- Table principale du personnel
CREATE TABLE personnel (
    id_personnel SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    type_personnel VARCHAR(20) NOT NULL CHECK (type_personnel IN ('militaire_interne', 'militaire_externe', 'civil_externe')),
    id_grade INT REFERENCES grade(id_grade),
    id_unite INT REFERENCES unite(id_unite),
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    actif BOOLEAN NOT NULL DEFAULT TRUE
);

-- Informations spécifiques aux militaires externes
CREATE TABLE militaire_externe_info (
    id_militaire_externe SERIAL PRIMARY KEY,
    id_personnel INT REFERENCES personnel(id_personnel) ON DELETE CASCADE,
    cin VARCHAR(20) NOT NULL,
    unite_origine VARCHAR(100),
    destination VARCHAR(100),
    objet_visite TEXT,
    horaire_entree TIMESTAMP,
    horaire_sortie_prevue TIMESTAMP,
    accompagnateur VARCHAR(100),
    UNIQUE(id_personnel)
);

-- Informations spécifiques aux civils externes
CREATE TABLE civil_info (
    id_civil SERIAL PRIMARY KEY,
    id_personnel INT REFERENCES personnel(id_personnel) ON DELETE CASCADE,
    cin VARCHAR(20) NOT NULL,
    societe VARCHAR(100),
    destination VARCHAR(100),
    objet_visite TEXT,
    horaire_entree TIMESTAMP,
    horaire_sortie_prevue TIMESTAMP,
    accompagnateur VARCHAR(100),
    UNIQUE(id_personnel)
);

-- ========== ATTRIBUTIONS ==========

CREATE TABLE attribution_badge (
    id_attribution SERIAL PRIMARY KEY,
    id_personnel INT REFERENCES personnel(id_personnel) NOT NULL,
    id_badge INT REFERENCES badge(id_badge) NOT NULL,
    date_attribution TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_fin TIMESTAMP,
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'expire', 'cloture', 'perdu')),
    attribue_par VARCHAR(50), -- Service des badges, Agent accueil, etc.
    motif_cloture TEXT,
    UNIQUE(id_badge, statut) DEFERRABLE INITIALLY DEFERRED -- Un badge ne peut avoir qu'une attribution active
);

-- ========== PASSAGES ==========

CREATE TABLE passage (
    id_journal SERIAL PRIMARY KEY,
    id_badge INT REFERENCES badge(id_badge),
    id_porte INT REFERENCES porte(id_porte) NOT NULL,
    epc_code VARCHAR(32), -- Stocké même si badge non reconnu
    date_acces TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    type_acces VARCHAR(10) NOT NULL CHECK (type_acces IN ('entree', 'sortie')),
    resultat VARCHAR(15) NOT NULL CHECK (resultat IN ('autorise', 'refuse')),
    motif_refus VARCHAR(100), -- Badge inconnu, expiré, etc.
    agent_controle VARCHAR(50)
);

-- ========== INDEX POUR PERFORMANCE ==========

CREATE INDEX idx_personnel_type ON personnel(type_personnel);
CREATE INDEX idx_personnel_actif ON personnel(actif);
CREATE INDEX idx_badge_actif ON badge(actif);
CREATE INDEX idx_attribution_statut ON attribution_badge(statut);
CREATE INDEX idx_attribution_badge_actif ON attribution_badge(id_badge, statut);
CREATE INDEX idx_passage_date ON passage(date_acces);
CREATE INDEX idx_passage_badge ON passage(id_badge);
CREATE INDEX idx_passage_epc ON passage(epc_code);

-- ========== DONNÉES DE RÉFÉRENCE ==========

-- Types de badges selon la logique métier
INSERT INTO type_badge (nom_type_badge, permanent, reutilisable, description) VALUES 
('Badge Militaire Interne', TRUE, FALSE, 'Badge permanent pour militaires internes, non réutilisable'),
('Badge Visiteur Militaire', FALSE, TRUE, 'Badge temporaire pour militaires externes, réutilisable'),
('Badge Visiteur Civil', FALSE, TRUE, 'Badge temporaire pour civils externes, réutilisable');

-- Grades militaires
INSERT INTO grade (nom_grade) VALUES 
('Général'), ('Colonel'), ('Lieutenant-Colonel'), ('Commandant'), ('Capitaine'),
('Lieutenant'), ('Sous-Lieutenant'), ('Adjudant-Chef'), ('Adjudant'), ('Sergent-Chef'),
('Sergent'), ('Caporal-Chef'), ('Caporal'), ('Soldat de 1ère classe'), ('Soldat');

-- Unités militaires
INSERT INTO unite (nom_unite) VALUES 
('État-Major'), ('1er Régiment d''Infanterie'), ('2ème Régiment de Cavalerie'),
('3ème Régiment d''Artillerie'), ('Service de Santé'), ('Service Technique'),
('Service Logistique'), ('Service de Sécurité'), ('Service des Transmissions'),
('Service du Génie');

-- Portes d'accès
INSERT INTO porte (libelle, localisation) VALUES 
('Entrée Principale', 'Accès principal du site'),
('Sortie Principale', 'Sortie principale du site'),
('Accès Parking', 'Entrée parking personnel'),
('Entrée Service', 'Accès services techniques'),
('Accès VIP', 'Entrée pour visiteurs officiels'),
('Sortie Secours', 'Sortie de secours'),
('Accès Mess', 'Entrée mess des officiers'),
('Accès Infirmerie', 'Entrée service de santé');

-- ========== VUES MÉTIER ==========

-- Vue des attributions actives avec détails complets
CREATE VIEW v_attributions_actives AS
SELECT 
    ab.id_attribution,
    ab.id_personnel,
    ab.id_badge,
    p.nom,
    p.prenom,
    p.type_personnel,
    g.nom_grade,
    u.nom_unite,
    b.epc_code,
    tb.nom_type_badge,
    tb.permanent,
    tb.reutilisable,
    ab.date_attribution,
    ab.attribue_par
FROM attribution_badge ab
JOIN personnel p ON ab.id_personnel = p.id_personnel
JOIN badge b ON ab.id_badge = b.id_badge
JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
LEFT JOIN grade g ON p.id_grade = g.id_grade
LEFT JOIN unite u ON p.id_unite = u.id_unite
WHERE ab.statut = 'actif' AND p.actif = TRUE AND b.actif = TRUE;

-- Vue des badges disponibles pour attribution
CREATE VIEW v_badges_disponibles AS
SELECT 
    b.id_badge,
    b.epc_code,
    tb.nom_type_badge,
    tb.permanent,
    tb.reutilisable,
    tb.description
FROM badge b
JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
LEFT JOIN attribution_badge ab ON b.id_badge = ab.id_badge AND ab.statut = 'actif'
WHERE b.actif = TRUE AND ab.id_badge IS NULL;

-- Vue des passages avec informations complètes
CREATE VIEW v_passages_complets AS
SELECT 
    pa.id_journal,
    pa.epc_code,
    pa.date_acces,
    pa.type_acces,
    pa.resultat,
    pa.motif_refus,
    po.libelle as nom_porte,
    po.localisation,
    p.nom,
    p.prenom,
    p.type_personnel,
    g.nom_grade,
    u.nom_unite,
    tb.nom_type_badge
FROM passage pa
JOIN porte po ON pa.id_porte = po.id_porte
LEFT JOIN badge b ON pa.id_badge = b.id_badge
LEFT JOIN attribution_badge ab ON b.id_badge = ab.id_badge AND ab.statut = 'actif'
LEFT JOIN personnel p ON ab.id_personnel = p.id_personnel
LEFT JOIN type_badge tb ON b.id_type_badge = tb.id_type_badge
LEFT JOIN grade g ON p.id_grade = g.id_grade
LEFT JOIN unite u ON p.id_unite = u.id_unite;

-- ========== FONCTIONS MÉTIER ==========

-- Fonction pour obtenir un badge disponible par type
CREATE OR REPLACE FUNCTION get_badge_disponible(type_badge_id INT)
RETURNS INT AS $$
DECLARE
    badge_id INT;
BEGIN
    SELECT b.id_badge INTO badge_id
    FROM v_badges_disponibles b
    WHERE b.id_type_badge = type_badge_id
    LIMIT 1;
    
    RETURN badge_id;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour clôturer automatiquement les attributions expirées
CREATE OR REPLACE FUNCTION cloture_attributions_expirees()
RETURNS INT AS $$
DECLARE
    nb_clotures INT;
BEGIN
    UPDATE attribution_badge 
    SET statut = 'expire', 
        date_fin = CURRENT_TIMESTAMP,
        motif_cloture = 'Expiration automatique'
    WHERE statut = 'actif' 
    AND id_personnel IN (
        SELECT p.id_personnel 
        FROM personnel p
        JOIN civil_info ci ON p.id_personnel = ci.id_personnel
        WHERE ci.horaire_sortie_prevue < CURRENT_TIMESTAMP
        UNION
        SELECT p.id_personnel 
        FROM personnel p
        JOIN militaire_externe_info mei ON p.id_personnel = mei.id_personnel
        WHERE mei.horaire_sortie_prevue < CURRENT_TIMESTAMP
    );
    
    GET DIAGNOSTICS nb_clotures = ROW_COUNT;
    RETURN nb_clotures;
END;
$$ LANGUAGE plpgsql;

COMMENT ON DATABASE access_control IS 'Système de contrôle d''accès militaire avec gestion des badges RFID';
COMMENT ON TABLE personnel IS 'Personnel autorisé : militaires internes, militaires externes, civils externes';
COMMENT ON TABLE militaire_externe_info IS 'Informations spécifiques aux militaires visiteurs';
COMMENT ON TABLE civil_info IS 'Informations spécifiques aux civils visiteurs';
COMMENT ON TABLE badge IS 'Badges RFID avec types permanent/temporaire';
COMMENT ON TABLE attribution_badge IS 'Historique des attributions de badges avec règles métier';
COMMENT ON TABLE passage IS 'Journal des passages aux points de contrôle';
