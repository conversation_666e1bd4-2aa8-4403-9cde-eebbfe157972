import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
});

// ========== PERSONNEL ==========
export const personnelAPI = {
  // Personnel Interne
  getPersonnelsInternes: () => api.get('/personnel/internes'),
  getPersonnelInterneById: (id) => api.get(`/personnel/internes/${id}`),
  createPersonnelInterne: (data) => api.post('/personnel/internes', data),
  updatePersonnelInterne: (id, data) => api.put(`/personnel/internes/${id}`, data),
  deletePersonnelInterne: (id, type = 'logique') => api.delete(`/personnel/internes/${id}?type=${type}`),

  // Personnel Externe
  getPersonnelsExternes: () => api.get('/personnel/externes'),
  getPersonnelExterneById: (id) => api.get(`/personnel/externes/${id}`),
  createPersonnelExterne: (data) => api.post('/personnel/externes', data),
  updatePersonnelExterne: (id, data) => api.put(`/personnel/externes/${id}`, data),
  deletePersonnelExterne: (id) => api.delete(`/personnel/externes/${id}`),

  // Tous les personnels (compatibilité)
  getAllPersonnels: () => api.get('/personnels'),
  createPersonnel: (data) => api.post('/personnels', data),
};

// ========== BADGES ==========
export const badgeAPI = {
  getAllBadges: () => api.get('/badges'),
  getBadgesDisponibles: (typeBadge) => api.get(`/badges/disponibles${typeBadge ? `?type_badge=${typeBadge}` : ''}`),
  getBadgeById: (id) => api.get(`/badges/${id}`),
  createBadge: (data) => api.post('/badges', data),
  updateBadge: (id, data) => api.put(`/badges/${id}`, data),
  deleteBadge: (id) => api.delete(`/badges/${id}`),
};

// ========== ATTRIBUTIONS ==========
export const attributionAPI = {
  getAllAttributions: (params = {}) => api.get('/attributions', { params }),
  getAttributionsActives: () => api.get('/attributions/actives'),
  getAttributionById: (id) => api.get(`/attributions/${id}`),
  createAttribution: (data) => api.post('/attributions', data),
  cloturerAttribution: (id, data) => api.put(`/attributions/${id}/fin`, data),
  getHistoriquePersonnel: (idPersonnel) => api.get(`/attributions/personnel/${idPersonnel}`),
};

// ========== PASSAGES ==========
export const passageAPI = {
  getAllPassages: (params = {}) => api.get('/passages', { params }),
  getPassagesRecents: (limit = 20) => api.get(`/passages/recent?limit=${limit}`),
  getPassagesPersonnel: (idPersonnel, params = {}) => api.get(`/passages/personnel/${idPersonnel}`, { params }),
  getPassagesBadge: (idBadge, params = {}) => api.get(`/passages/badge/${idBadge}`, { params }),
  getStatistiquesPassages: (params = {}) => api.get('/passages/statistiques', { params }),
  createPassage: (data) => api.post('/passages', data),
};

// ========== RÉFÉRENTIELS ==========
export const referentielAPI = {
  // Types
  getTypesBadge: () => api.get('/types_badge'),
  createTypeBadge: (data) => api.post('/types_badge', data),
  getTypesPersonnel: () => api.get('/types_personnel'),
  createTypePersonnel: (data) => api.post('/types_personnel', data),

  // Unités
  getUnites: () => api.get('/unites'),
  createUnite: (data) => api.post('/unites', data),
  updateUnite: (id, data) => api.put(`/unites/${id}`, data),
  deleteUnite: (id) => api.delete(`/unites/${id}`),

  // Grades
  getGrades: () => api.get('/grades'),
  createGrade: (data) => api.post('/grades', data),
  updateGrade: (id, data) => api.put(`/grades/${id}`, data),
  deleteGrade: (id) => api.delete(`/grades/${id}`),

  // Portes
  getPortes: () => api.get('/portes'),
  createPorte: (data) => api.post('/portes', data),
  updatePorte: (id, data) => api.put(`/portes/${id}`, data),
  deletePorte: (id) => api.delete(`/portes/${id}`),
};

// ========== UTILITAIRES ==========
export const utilAPI = {
  ping: () => api.get('/ping'),
};

export default api;